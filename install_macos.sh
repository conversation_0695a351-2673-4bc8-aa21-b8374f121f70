#!/bin/bash
# MT5 Trading Bot - macOS Installation Script
# Tự động cài đặt và cấu hình bot trên macOS

set -e  # Exit on any error

echo "🍎 MT5 Trading Bot - macOS Installation"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is for macOS only!"
    exit 1
fi

print_info "Detected macOS system"

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    print_warning "Homebrew not found. Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
else
    print_status "Homebrew found"
fi

# Update Homebrew
print_info "Updating Homebrew..."
brew update

# Install Python if not available
if ! command -v python3 &> /dev/null; then
    print_warning "Python3 not found. Installing Python..."
    brew install python@3.11
else
    print_status "Python3 found: $(python3 --version)"
fi

# Install TA-Lib (required for technical indicators)
print_info "Installing TA-Lib..."
if brew list ta-lib &> /dev/null; then
    print_status "TA-Lib already installed"
else
    brew install ta-lib
    print_status "TA-Lib installed"
fi

# Create virtual environment
print_info "Creating virtual environment..."
if [ -d "venv" ]; then
    print_warning "Virtual environment already exists"
    read -p "Do you want to recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf venv
        python3 -m venv venv
        print_status "Virtual environment recreated"
    else
        print_info "Using existing virtual environment"
    fi
else
    python3 -m venv venv
    print_status "Virtual environment created"
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_info "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
print_info "Installing Python dependencies..."
if [ -f "requirements-macos.txt" ]; then
    pip install -r requirements-macos.txt
    print_status "Dependencies installed from requirements-macos.txt"
else
    print_warning "requirements-macos.txt not found, using requirements.txt"
    pip install -r requirements.txt
fi

# Install TA-Lib Python package
print_info "Installing TA-Lib Python package..."
pip install TA-Lib

# Create configuration file
print_info "Setting up configuration..."
if [ ! -f "config/config.yaml" ]; then
    if [ -f "config/config.example.yaml" ]; then
        cp config/config.example.yaml config/config.yaml
        print_status "Configuration file created from example"
        print_warning "Please edit config/config.yaml with your MT5 credentials"
    else
        print_error "config.example.yaml not found"
    fi
else
    print_status "Configuration file already exists"
fi

# Create activation script
print_info "Creating activation script..."
cat > activate_env.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
echo "✅ Virtual environment activated!"
echo "📍 Project: $(pwd)"
echo "🐍 Python: $(which python)"
echo "📦 Pip: $(which pip)"
echo ""
echo "🚨 IMPORTANT: This is a MOCK MT5 client on macOS"
echo "   No real trading will occur - for development/testing only"
echo ""
echo "🚀 Available commands:"
echo "   python main.py          # Run bot (mock mode)"
echo "   python run_tests.py     # Run tests"
echo "   python run_backtest.py  # Run backtest"
echo "   python quick_start.py   # Validate setup"
echo ""
echo "💡 To deactivate: deactivate"
exec "$SHELL"
EOF

chmod +x activate_env.sh
print_status "Activation script created"

# Test installation
print_info "Testing installation..."
python -c "
import sys
print(f'✅ Python: {sys.version}')

try:
    import pandas
    print(f'✅ Pandas: {pandas.__version__}')
except ImportError:
    print('❌ Pandas not found')

try:
    import numpy
    print(f'✅ Numpy: {numpy.__version__}')
except ImportError:
    print('❌ Numpy not found')

try:
    import talib
    print('✅ TA-Lib: OK')
except ImportError:
    print('❌ TA-Lib not found')

try:
    import yfinance
    print('✅ yfinance: OK')
except ImportError:
    print('❌ yfinance not found')
"

# Final instructions
echo ""
echo "🎉 Installation completed!"
echo "========================="
echo ""
print_info "Next steps:"
echo "1. Edit config/config.yaml with your MT5 credentials"
echo "2. Run: ./activate_env.sh"
echo "3. Test: python quick_start.py"
echo "4. Run bot: python main.py"
echo ""
print_warning "IMPORTANT NOTES:"
echo "• This is a MOCK MT5 client on macOS - no real trading!"
echo "• For real trading, use Windows with actual MT5 terminal"
echo "• This setup is perfect for development and backtesting"
echo ""
print_info "Documentation:"
echo "• Quick guide: HUONG_DAN_NHANH.md"
echo "• Virtual env guide: docs/VIRTUAL_ENVIRONMENT_GUIDE.md"
echo "• Full documentation: docs/README_DETAILED.md"
echo ""
echo "Happy coding! 🚀"

#!/usr/bin/env python3
"""
Test Enhanced Risk Management System
Quick test to verify the enhanced risk management features
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.risk.risk_manager import RiskManager
from src.utils.config import load_config
from src.core.mt5_client import PositionInfo

def test_enhanced_risk_management():
    """Test enhanced risk management features"""
    print("🛡️  Testing Enhanced Risk Management System")
    print("=" * 60)
    
    try:
        # Load config
        config = load_config()
        
        # Initialize risk manager
        risk_manager = RiskManager(config.risk, config.trading)
        
        # Test enhanced position sizing
        print("\n📊 Testing Enhanced Position Sizing:")
        
        # Mock symbol info
        symbol_info = {
            'contract_size': 100,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 1.0,
            'volume_step': 0.01
        }
        
        # Test different market conditions
        test_scenarios = [
            {
                'name': 'High Confidence + Low Volatility + Trending',
                'signal_confidence': 0.9,
                'market_volatility': 'low',
                'market_regime': 'trending'
            },
            {
                'name': 'Low Confidence + High Volatility + Ranging',
                'signal_confidence': 0.3,
                'market_volatility': 'high',
                'market_regime': 'ranging'
            },
            {
                'name': 'Medium Confidence + Extreme Volatility + Breakout',
                'signal_confidence': 0.6,
                'market_volatility': 'extreme',
                'market_regime': 'breakout'
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n🎯 Scenario: {scenario['name']}")
            
            position_size = risk_manager.calculate_position_size(
                account_balance=10000.0,
                entry_price=2000.0,
                stop_loss=1980.0,
                atr_value=15.0,
                symbol_info=symbol_info,
                signal_confidence=scenario['signal_confidence'],
                market_volatility=scenario['market_volatility'],
                market_regime=scenario['market_regime']
            )
            
            print(f"  Volume: {position_size.volume:.3f}")
            print(f"  Risk Amount: ${position_size.risk_amount:.2f}")
            print(f"  Confidence Multiplier: {position_size.confidence_multiplier:.2f}")
            print(f"  Volatility Adjustment: {position_size.volatility_adjustment:.2f}")
            print(f"  Max Position Time: {position_size.max_position_time} minutes")
            print(f"  Risk-Reward Ratio: {position_size.risk_reward_ratio:.2f}")
        
        # Test dynamic position timeout
        print("\n⏰ Testing Dynamic Position Timeout:")
        
        timeout_scenarios = [
            ('low', 'trending'),
            ('high', 'ranging'),
            ('extreme', 'reversal'),
            ('medium', 'breakout')
        ]
        
        for volatility, regime in timeout_scenarios:
            timeout = risk_manager._calculate_position_timeout(volatility, regime)
            print(f"  {volatility.capitalize()} volatility + {regime} market: {timeout} minutes")
        
        # Test consecutive wins/losses tracking
        print("\n📈 Testing Consecutive Wins/Losses Tracking:")
        
        # Simulate some trades
        trade_results = [100, -50, 75, -30, 120, -40, 90, -25, 150]
        
        for i, profit in enumerate(trade_results):
            risk_manager.update_trade_result(profit)
            print(f"  Trade {i+1}: ${profit:+.0f} | "
                  f"Wins: {risk_manager.consecutive_wins}, "
                  f"Losses: {risk_manager.consecutive_losses}")
        
        # Test risk adjustments after consecutive losses
        print("\n🔻 Testing Risk Adjustments After Consecutive Losses:")
        
        # Simulate 4 consecutive losses
        for i in range(4):
            risk_manager.update_trade_result(-50)
        
        adjustments = risk_manager._calculate_risk_adjustments(0.8, 'medium', 'trending')
        print(f"  After {risk_manager.consecutive_losses} losses:")
        print(f"  Total Risk Multiplier: {adjustments['total_multiplier']:.2f}")
        print(f"  Volume Multiplier: {adjustments['volume_multiplier']:.2f}")
        
        # Test enhanced position closing
        print("\n🚪 Testing Enhanced Position Closing:")
        
        # Create mock position
        mock_position = PositionInfo(
            ticket=12345,
            symbol='XAUUSD',
            type=1,  # Long position
            volume=0.1,
            price_open=2000.0,
            price_current=2010.0,
            profit=100.0,
            swap=0.0,
            commission=-3.0,
            time=datetime.now() - timedelta(hours=5)  # 5 hours old
        )
        
        # Mock market state with bearish crossover
        class MockMarketState:
            def __init__(self):
                self.market_regime = type('obj', (object,), {
                    'volatility_regime': 'medium',
                    'market_phase': 'trending'
                })()
                self.macd_signal = type('obj', (object,), {
                    'crossover': 'bearish_cross'
                })()
        
        market_state = MockMarketState()
        
        should_close, reason = risk_manager.should_close_position_enhanced(
            mock_position, 2010.0, 15.0, market_state
        )
        
        print(f"  Should close position: {should_close}")
        print(f"  Reason: {reason}")
        
        print("\n✅ Enhanced Risk Management System working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced risk management: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_risk_management()
    sys.exit(0 if success else 1)

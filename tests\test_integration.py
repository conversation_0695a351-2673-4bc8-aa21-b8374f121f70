"""
Integration tests for MT5 Trading Bot
Tests the interaction between different components
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os

from src.indicators.technical_indicators import TechnicalIndicators
from src.risk.risk_manager import RiskManager
from src.strategy.gold_strategy import GoldTradingStrategy
from src.backtesting.backtest_engine import BacktestEngine
from src.data.data_manager import DataManager, DatabaseManager
from src.utils.config import BotConfig, MT5Config, TradingConfig, RiskConfig, AIConfig, BacktestConfig


class TestIntegration:
    """Integration test cases"""

    def setup_method(self):
        """Setup test fixtures"""
        # Create test configuration
        self.config = BotConfig(
            mt5=MT5Config(
                login=12345,
                password="test_password",
                server="test_server",
                symbol="XAUUSDc",
                magic_number=234000
            ),
            trading=TradingConfig(
                timeframe=5,
                max_positions=2,
                max_daily_trades=5,
                risk_per_trade=0.02,
                min_volume=0.01,
                max_volume=0.5,
                volume_step=0.01
            ),
            risk=RiskConfig(
                max_drawdown=0.10,
                stop_loss_atr_multiplier=2.0,
                take_profit_atr_multiplier=3.0,
                trailing_stop=True,
                trailing_stop_distance=50,
                max_spread=30
            ),
            ai=AIConfig(
                model_type="dqn",
                learning_rate=0.001,
                batch_size=32,
                memory_size=1000,
                epsilon_start=1.0,
                epsilon_end=0.01,
                epsilon_decay=0.995,
                target_update_frequency=100,
                training_frequency=4
            ),
            backtest=BacktestConfig(
                start_date="2024-01-01",
                end_date="2024-02-01",
                initial_balance=10000.0,
                commission=0.0,
                spread=20
            ),
            enable_ai=False,  # Disable AI for simpler testing
            live_trading=False
        )

        # Create sample market data
        self.sample_data = self._create_sample_data()

    def _create_sample_data(self) -> pd.DataFrame:
        """Create realistic sample market data"""
        dates = pd.date_range('2024-01-01', periods=1000, freq='5min')
        np.random.seed(42)  # For reproducible tests

        # Generate realistic gold price data
        base_price = 2000.0
        price_changes = np.random.normal(0, 0.002, 1000)  # 0.2% volatility

        prices = [base_price]
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        # Create OHLC data
        data = []
        for i, price in enumerate(prices):
            high = price * (1 + abs(np.random.normal(0, 0.001)))
            low = price * (1 - abs(np.random.normal(0, 0.001)))
            open_price = prices[i-1] if i > 0 else price
            close_price = price

            data.append({
                'time': dates[i],
                'open': open_price,
                'high': max(open_price, close_price, high),
                'low': min(open_price, close_price, low),
                'close': close_price,
                'volume': np.random.randint(100, 1000),
                'spread': np.random.uniform(15, 25),
                'tick_volume': np.random.randint(50, 500)
            })

        df = pd.DataFrame(data)
        df.set_index('time', inplace=True)
        return df

    def test_indicators_integration(self):
        """Test technical indicators integration"""
        indicators = TechnicalIndicators()

        # Test all indicators work together
        combined_signal = indicators.get_combined_signal(self.sample_data)

        assert isinstance(combined_signal, dict)
        assert 'macd' in combined_signal
        assert 'atr' in combined_signal
        assert 'pivot_points' in combined_signal
        assert 'signal_strength' in combined_signal
        assert 'recommendation' in combined_signal

        # Verify signal strength is valid
        assert 0 <= combined_signal['signal_strength'] <= 1

        # Verify recommendation is valid
        valid_recommendations = ['strong_buy', 'buy', 'hold', 'sell', 'strong_sell', 'wait']
        assert combined_signal['recommendation'] in valid_recommendations

    @patch('src.core.mt5_client.MT5Client')
    def test_strategy_risk_integration(self, mock_mt5_client):
        """Test strategy and risk manager integration"""
        # Mock MT5 client
        mock_client = Mock()
        mock_client.get_symbol_info.return_value = {
            'contract_size': 100,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 1.0,
            'volume_step': 0.01,
            'spread': 20
        }
        mock_client.get_current_price.return_value = (2000.0, 2000.2)

        # Create strategy
        strategy = GoldTradingStrategy(self.config, mock_client)

        # Mock market state
        market_state = strategy.analyze_market()

        # Test signal generation with risk management
        account_info = {
            'balance': 10000,
            'equity': 10000,
            'margin_level': 500
        }

        signal = strategy.generate_signal(market_state, [], account_info)

        # Signal might be None (no trading opportunity) or a valid signal
        if signal is not None:
            assert signal.volume >= self.config.trading.min_volume
            assert signal.volume <= self.config.trading.max_volume
            assert signal.stop_loss != signal.entry_price
            assert signal.take_profit != signal.entry_price

    def test_backtest_integration(self):
        """Test backtesting integration with all components"""
        # Create backtest engine
        backtest_engine = BacktestEngine(self.config)

        # Run backtest on sample data
        results = backtest_engine.run_backtest(
            data=self.sample_data,
            start_date=self.sample_data.index[100],  # Skip initial data for indicators
            end_date=self.sample_data.index[500]
        )

        # Verify results structure
        assert results.initial_balance == self.config.backtest.initial_balance
        assert results.final_balance > 0
        assert results.total_trades >= 0
        assert results.winning_trades >= 0
        assert results.losing_trades >= 0
        assert results.winning_trades + results.losing_trades == results.total_trades

        if results.total_trades > 0:
            assert 0 <= results.win_rate <= 1
            assert len(results.trades) == results.total_trades

            # Check trade data integrity
            for trade in results.trades:
                assert trade.entry_time <= trade.exit_time
                assert trade.volume > 0
                assert trade.direction in ['long', 'short']
                assert trade.duration_minutes >= 0

    def test_data_manager_integration(self):
        """Test data manager integration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")

            # Mock MT5 client
            mock_client = Mock()
            mock_client.get_historical_data.return_value = self.sample_data

            # Create data manager
            data_manager = DataManager(mock_client, db_path)

            # Test data retrieval and preprocessing
            processed_data = data_manager.get_market_data(use_cache=False)

            assert processed_data is not None
            assert len(processed_data) > 0

            # Check if preprocessing features are added
            expected_features = ['price_change', 'price_range', 'body_size']
            for feature in expected_features:
                assert feature in processed_data.columns

    def test_database_integration(self):
        """Test database operations integration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, "test.db")

            # Create database manager
            db_manager = DatabaseManager(db_path)

            # Test storing and retrieving market data
            from src.data.data_manager import MarketData

            test_data = [
                MarketData(
                    timestamp=datetime.now(),
                    symbol="XAUUSDc",
                    timeframe=5,
                    open=2000.0,
                    high=2005.0,
                    low=1995.0,
                    close=2002.0,
                    volume=1000,
                    spread=20.0,
                    tick_volume=500
                )
            ]

            # Store data
            db_manager.store_market_data(test_data)

            # Retrieve data
            start_date = datetime.now() - timedelta(hours=1)
            end_date = datetime.now() + timedelta(hours=1)

            retrieved_data = db_manager.get_market_data(
                "XAUUSDc", 5, start_date, end_date
            )

            assert len(retrieved_data) == 1
            assert retrieved_data.iloc[0]['open'] == 2000.0
            assert retrieved_data.iloc[0]['close'] == 2002.0

    def test_signal_generation_pipeline(self):
        """Test complete signal generation pipeline"""
        # Mock MT5 client
        mock_client = Mock()
        mock_client.get_symbol_info.return_value = {
            'contract_size': 100,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 1.0,
            'volume_step': 0.01,
            'spread': 20
        }
        mock_client.get_current_price.return_value = (2000.0, 2000.2)
        mock_client.get_historical_data.return_value = self.sample_data

        # Create components
        indicators = TechnicalIndicators()
        risk_manager = RiskManager(self.config.risk, self.config.trading)
        strategy = GoldTradingStrategy(self.config, mock_client)

        # Test signal generation pipeline
        # 1. Get market data
        market_data = self.sample_data

        # 2. Calculate indicators
        macd_signal = indicators.calculate_macd(market_data)
        atr_data = indicators.calculate_atr(market_data)
        pivot_points = indicators.calculate_pivot_points(market_data)

        # 3. Check risk conditions
        account_info = {
            'balance': 10000,
            'equity': 10000,
            'margin_level': 500
        }

        trading_allowed, reason = risk_manager.check_trading_allowed([], account_info)
        assert trading_allowed

        # 4. Generate trading signal
        market_state = strategy.analyze_market()
        signal = strategy.generate_signal(market_state, [], account_info)

        # Signal generation should complete without errors
        # Signal might be None if no trading opportunity
        if signal is not None:
            assert signal.action in ['buy', 'sell', 'hold', 'close']
            assert signal.strength >= 0
            assert signal.volume > 0

    def test_error_handling_integration(self):
        """Test error handling across components"""
        # Test with invalid data
        invalid_data = pd.DataFrame()

        indicators = TechnicalIndicators()

        # Should handle empty data gracefully
        macd_result = indicators.calculate_macd(invalid_data)
        assert macd_result.trend == 'neutral'

        atr_result = indicators.calculate_atr(invalid_data)
        assert atr_result.atr == 0

        pivot_result = indicators.calculate_pivot_points(invalid_data)
        assert pivot_result.current_level == 'at_pivot'

    def test_configuration_integration(self):
        """Test configuration integration across components"""
        # Test that configuration is properly used across components
        risk_manager = RiskManager(self.config.risk, self.config.trading)

        # Check that risk parameters are applied
        assert risk_manager.risk_config.max_drawdown == 0.10
        assert risk_manager.trading_config.max_positions == 2

        # Test position size calculation uses config
        symbol_info = {
            'contract_size': 100,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 1.0,
            'volume_step': 0.01
        }

        position_size = risk_manager.calculate_position_size(
            10000, 2000.0, 1980.0, 15.0, symbol_info
        )

        # Should respect volume limits from config
        assert position_size.volume >= self.config.trading.min_volume
        assert position_size.volume <= self.config.trading.max_volume

        # Risk amount should match config
        expected_risk = 10000 * self.config.trading.risk_per_trade
        assert abs(position_size.risk_amount - expected_risk) < 0.01


if __name__ == '__main__':
    pytest.main([__file__])

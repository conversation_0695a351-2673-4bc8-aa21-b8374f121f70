#!/usr/bin/env python3
"""
Test Market Adaptation Engine
Quick test to verify the market adaptation features
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.strategy.market_adaptation import MarketAdaptationEngine, MarketCondition
from src.indicators.technical_indicators import MarketRegime
from src.utils.config import load_config

def create_market_scenarios():
    """Create different market scenarios for testing"""
    np.random.seed(42)
    
    scenarios = {}
    
    # Trending market (strong uptrend)
    trending_data = []
    price = 2000
    for i in range(100):
        price += np.random.normal(2, 5)  # Upward bias with some noise
        high = price + abs(np.random.normal(0, 2))
        low = price - abs(np.random.normal(0, 2))
        trending_data.append({
            'open': price - np.random.normal(0, 1),
            'high': high,
            'low': low,
            'close': price,
            'volume': np.random.randint(100, 1000)
        })
    scenarios['trending'] = pd.DataFrame(trending_data)
    
    # Ranging market (sideways movement)
    ranging_data = []
    price = 2000
    for i in range(100):
        price += np.random.normal(0, 3)  # No bias, just noise
        price = max(1980, min(2020, price))  # Keep in range
        high = price + abs(np.random.normal(0, 2))
        low = price - abs(np.random.normal(0, 2))
        ranging_data.append({
            'open': price - np.random.normal(0, 1),
            'high': high,
            'low': low,
            'close': price,
            'volume': np.random.randint(100, 1000)
        })
    scenarios['ranging'] = pd.DataFrame(ranging_data)
    
    # High volatility market
    volatile_data = []
    price = 2000
    for i in range(100):
        price += np.random.normal(0, 15)  # High volatility
        high = price + abs(np.random.normal(0, 8))
        low = price - abs(np.random.normal(0, 8))
        volatile_data.append({
            'open': price - np.random.normal(0, 5),
            'high': high,
            'low': low,
            'close': price,
            'volume': np.random.randint(100, 1000)
        })
    scenarios['volatile'] = pd.DataFrame(volatile_data)
    
    return scenarios

def test_market_adaptation():
    """Test market adaptation engine"""
    print("🎯 Testing Market Adaptation Engine")
    print("=" * 50)
    
    try:
        # Load config
        config = load_config()
        
        # Initialize adaptation engine
        adaptation_engine = MarketAdaptationEngine(config)
        
        # Create market scenarios
        scenarios = create_market_scenarios()
        
        print("\n📊 Testing Market Condition Assessment:")
        
        for scenario_name, data in scenarios.items():
            print(f"\n🔍 Scenario: {scenario_name.upper()}")
            
            # Create mock market regime
            if scenario_name == 'trending':
                market_regime = MarketRegime('bullish', 0.8, 'medium', 'trending')
            elif scenario_name == 'ranging':
                market_regime = MarketRegime('sideways', 0.2, 'low', 'ranging')
            else:  # volatile
                market_regime = MarketRegime('bearish', 0.6, 'extreme', 'breakout')
            
            # Assess market condition
            condition = adaptation_engine.assess_market_condition(data, market_regime)
            
            print(f"  Volatility Level: {condition.volatility_level}")
            print(f"  Trend Strength: {condition.trend_strength:.2f}")
            print(f"  Market Phase: {condition.market_phase}")
            print(f"  Efficiency: {condition.efficiency:.2f}")
            print(f"  Noise Level: {condition.noise_level:.2f}")
            
            # Test parameter adaptation
            recent_performance = {'win_rate': 0.6, 'profit_factor': 1.2}
            adapted_params = adaptation_engine.adapt_parameters(condition, recent_performance)
            
            print(f"  Adapted MACD: ({adapted_params.macd_fast}, {adapted_params.macd_slow}, {adapted_params.macd_signal})")
            print(f"  Signal Threshold: {adapted_params.signal_threshold:.2f}")
            print(f"  Risk per Trade: {adapted_params.risk_per_trade:.3f}")
            print(f"  Stop Loss Multiplier: {adapted_params.stop_loss_multiplier:.1f}")
            print(f"  Take Profit Multiplier: {adapted_params.take_profit_multiplier:.1f}")
            print(f"  Max Position Time: {adapted_params.max_position_time} minutes")
        
        # Test performance tracking
        print("\n📈 Testing Performance Tracking:")
        
        # Simulate some trades in different market conditions
        trade_results = [
            {'market_phase': 'trending', 'pnl': 100},
            {'market_phase': 'trending', 'pnl': 150},
            {'market_phase': 'trending', 'pnl': -50},
            {'market_phase': 'ranging', 'pnl': -30},
            {'market_phase': 'ranging', 'pnl': 40},
            {'market_phase': 'breakout', 'pnl': 200},
            {'market_phase': 'breakout', 'pnl': -80},
        ]
        
        for trade in trade_results:
            adaptation_engine.update_performance(trade['market_phase'], trade)
        
        performance_by_condition = adaptation_engine.get_performance_by_condition()
        
        for condition, perf in performance_by_condition.items():
            if perf['trades'] > 0:
                print(f"  {condition}: {perf['trades']} trades, "
                      f"Win Rate: {perf['win_rate']:.1%}, "
                      f"Avg PnL: ${perf['avg_pnl']:.0f}")
        
        # Test adaptation summary
        print("\n📋 Testing Adaptation Summary:")
        
        summary = adaptation_engine.get_adaptation_summary()
        if summary:
            print(f"  Current Market Phase: {summary['market_phase']}")
            print(f"  Volatility Level: {summary['volatility_level']}")
            print(f"  MACD Settings: {summary['macd_settings']}")
            print(f"  Risk Settings: {summary['risk_settings']}")
        
        # Test adaptation with poor performance
        print("\n📉 Testing Adaptation with Poor Performance:")
        
        poor_performance = {'win_rate': 0.3, 'profit_factor': 0.6}
        trending_condition = MarketCondition('medium', 0.7, 'bullish', 'trending', 0.6, 0.3)
        
        conservative_params = adaptation_engine.adapt_parameters(trending_condition, poor_performance)
        
        print(f"  Conservative Signal Threshold: {conservative_params.signal_threshold:.2f}")
        print(f"  Conservative Risk per Trade: {conservative_params.risk_per_trade:.3f}")
        
        # Test adaptation with excellent performance
        print("\n📈 Testing Adaptation with Excellent Performance:")
        
        excellent_performance = {'win_rate': 0.8, 'profit_factor': 2.5}
        aggressive_params = adaptation_engine.adapt_parameters(trending_condition, excellent_performance)
        
        print(f"  Aggressive Signal Threshold: {aggressive_params.signal_threshold:.2f}")
        print(f"  Aggressive Risk per Trade: {aggressive_params.risk_per_trade:.3f}")
        
        print("\n✅ Market Adaptation Engine working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing market adaptation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_market_adaptation()
    sys.exit(0 if success else 1)

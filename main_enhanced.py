#!/usr/bin/env python3
"""
MT5 Python Trading Bot - Enhanced Main Entry Point
Tích hợp tất cả các cải tiến: Risk Management, Performance Tracking, Adaptive Strategy
"""

import sys
import signal
import asyncio
import argparse
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.enhanced_bot import EnhancedTradingBot
from src.performance.performance_tracker import PerformanceTracker
from src.performance.dashboard import PerformanceDashboard
from src.utils.logger import setup_logger
from src.utils.config import load_config

logger = setup_logger()


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print("\n🛑 Shutdown signal received. Stopping enhanced trading bot...")
    sys.exit(0)


def print_startup_banner():
    """Print enhanced startup banner"""
    print("=" * 80)
    print("🚀 MT5 ENHANCED TRADING BOT - PHIÊN BẢN NÂNG CẤP")
    print("=" * 80)
    print("✨ Tính năng mới:")
    print("   🛡️  Quản lý rủi ro nâng cao với Kelly Criterion")
    print("   📊 Chỉ báo kỹ thuật đa tầng (MACD + RSI + BB + Pivot)")
    print("   🌍 Thích ứng thị trường tự động")
    print("   📈 Theo dõi hiệu suất thời gian thực")
    print("   🎯 Tối ưu hóa tham số động")
    print("=" * 80)


def print_performance_summary(tracker: PerformanceTracker):
    """Print performance summary"""
    if not tracker.trades:
        print("📊 Chưa có giao dịch nào được thực hiện")
        return
    
    metrics = tracker.get_current_metrics()
    
    print("\n📊 TÓM TẮT HIỆU SUẤT:")
    print(f"   💰 Số dư hiện tại: ${tracker.current_balance:,.2f}")
    print(f"   📈 Tổng lợi nhuận: ${metrics.total_pnl:,.2f}")
    print(f"   🎯 Tỷ lệ thắng: {metrics.win_rate:.1%}")
    print(f"   📊 Profit Factor: {metrics.profit_factor:.2f}")
    print(f"   ⚠️  Max Drawdown: {metrics.max_drawdown_pct:.2%}")
    print(f"   📉 Drawdown hiện tại: {metrics.current_drawdown_pct:.2%}")
    print(f"   ⭐ Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
    print(f"   🔢 Tổng số lệnh: {metrics.total_trades}")


async def run_enhanced_bot(config, demo_mode=False, performance_tracking=True):
    """Run the enhanced trading bot"""
    try:
        # Initialize performance tracker if enabled
        tracker = None
        dashboard = None
        
        if performance_tracking:
            initial_balance = getattr(config.trading, 'initial_balance', 10000.0)
            tracker = PerformanceTracker(initial_balance=initial_balance)
            dashboard = PerformanceDashboard(tracker)
            logger.info("📊 Performance tracking enabled")
        
        # Initialize enhanced bot
        bot = EnhancedTradingBot(config, performance_tracker=tracker)
        
        if demo_mode:
            logger.info("🧪 DEMO MODE: Không có giao dịch thực sự")
            config.live_trading = False
        
        # Print initial status
        print(f"\n🎯 Cấu hình Bot:")
        print(f"   Symbol: {config.mt5.symbol}")
        print(f"   Timeframe: {getattr(config.trading, 'timeframe', 'M5')}")
        print(f"   Risk per trade: {getattr(config.risk, 'max_risk_per_trade', 0.02):.1%}")
        print(f"   Max positions: {getattr(config.risk, 'max_positions', 3)}")
        print(f"   Live trading: {'✅ BẬT' if config.live_trading else '❌ TẮT (Demo)'}")
        print(f"   Performance tracking: {'✅ BẬT' if performance_tracking else '❌ TẮT'}")
        
        # Start the bot
        logger.info("🚀 Starting enhanced trading bot...")
        await bot.start()
        
    except KeyboardInterrupt:
        logger.info("👋 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Error running enhanced bot: {e}")
        raise
    finally:
        # Print final performance summary
        if tracker:
            print_performance_summary(tracker)
            
            # Export final report
            if dashboard:
                try:
                    report_path = dashboard.export_dashboard_data()
                    print(f"\n📄 Báo cáo chi tiết đã được lưu: {report_path}")
                except Exception as e:
                    logger.warning(f"Failed to export dashboard data: {e}")


async def run_backtest_mode(config, period="1month", balance=10000.0):
    """Run backtest mode"""
    print(f"\n📈 CHẠY BACKTEST MODE")
    print(f"   Thời gian: {period}")
    print(f"   Số dư ban đầu: ${balance:,.2f}")
    
    try:
        from src.backtesting.backtest_engine import BacktestEngine, SimpleBacktestConfig
        
        # Create backtest config
        if period in ["1week", "1month", "3months", "6months", "1year"]:
            backtest_config = SimpleBacktestConfig.quick_setup(period, balance)
        else:
            # Custom period format: YYYY-MM-DD,YYYY-MM-DD
            if ',' in period:
                start_date, end_date = period.split(',')
                backtest_config = SimpleBacktestConfig(
                    start_date=start_date.strip(),
                    end_date=end_date.strip(),
                    initial_balance=balance
                )
            else:
                raise ValueError("Invalid period format. Use: 1month, 3months, or YYYY-MM-DD,YYYY-MM-DD")
        
        # Run backtest
        engine = BacktestEngine(config)
        results = engine.run_simple_backtest(backtest_config, config.mt5.symbol)
        
        # Print results
        print(f"\n🎯 KẾT QUẢ BACKTEST:")
        print(f"   📅 Thời gian: {results.start_date} đến {results.end_date}")
        print(f"   💰 Số dư cuối: ${results.final_balance:,.2f}")
        print(f"   📈 Lợi nhuận: ${results.total_return:,.2f} ({results.total_return_pct:.2f}%)")
        print(f"   🎯 Tỷ lệ thắng: {results.win_rate:.1%}")
        print(f"   📊 Profit Factor: {results.profit_factor:.2f}")
        print(f"   ⚠️  Max Drawdown: {results.max_drawdown_pct:.2%}")
        print(f"   ⭐ Sharpe Ratio: {results.sharpe_ratio:.2f}")
        print(f"   🔢 Tổng số lệnh: {results.total_trades}")
        
        # Generate detailed report
        engine.generate_report(results)
        print(f"\n📄 Báo cáo chi tiết đã được tạo trong thư mục reports/")
        
    except Exception as e:
        logger.error(f"❌ Backtest failed: {e}")
        raise


async def run_optimization_mode(config, mode="quick"):
    """Run optimization mode"""
    print(f"\n🔧 CHẠY OPTIMIZATION MODE: {mode.upper()}")
    
    try:
        from src.optimization.strategy_optimizer import StrategyOptimizer
        
        optimizer = StrategyOptimizer()
        
        if mode == "quick":
            max_combinations = 50
            print("   ⚡ Tối ưu hóa nhanh (50 tổ hợp)")
        elif mode == "comprehensive":
            max_combinations = 1000
            print("   🎯 Tối ưu hóa toàn diện (1000 tổ hợp)")
        else:
            max_combinations = 200
            print("   📊 Tối ưu hóa chuẩn (200 tổ hợp)")
        
        # Run optimization
        results = optimizer.optimize_strategy(
            symbol=config.mt5.symbol,
            max_combinations=max_combinations,
            parallel_workers=1
        )
        
        if 'error' not in results:
            opt_summary = results['optimization_summary']
            print(f"\n🎯 KẾT QUẢ TỐI ƯU HÓA:")
            print(f"   📊 Tổ hợp đã test: {opt_summary['total_combinations_tested']:,}")
            print(f"   ⭐ Điểm số tốt nhất: {opt_summary['best_overall_score']:.3f}")
            print(f"   📈 Lợi nhuận tốt nhất: {opt_summary['best_overall_return']:.2f}%")
            
            print(f"\n🏆 THAM SỐ TỐI ƯU:")
            best_params = opt_summary['best_overall_parameters']
            for param, value in best_params.items():
                print(f"   {param}: {value}")
            
            print(f"\n💡 KHUYẾN NGHỊ:")
            for i, rec in enumerate(results['recommendations'][:3], 1):
                print(f"   {i}. {rec}")
            
            print(f"\n📄 Báo cáo chi tiết đã được lưu trong reports/optimization/")
        else:
            print(f"❌ Optimization failed: {results['error']}")
            
    except Exception as e:
        logger.error(f"❌ Optimization failed: {e}")
        raise


def main():
    """Main entry point with enhanced features"""
    parser = argparse.ArgumentParser(description="MT5 Enhanced Trading Bot")
    parser.add_argument("--mode", default="live", 
                       choices=["live", "demo", "backtest", "optimize"],
                       help="Chế độ chạy bot")
    parser.add_argument("--period", default="1month",
                       help="Thời gian backtest (1week, 1month, 3months, 6months, 1year, hoặc YYYY-MM-DD,YYYY-MM-DD)")
    parser.add_argument("--balance", type=float, default=10000.0,
                       help="Số dư ban đầu cho backtest")
    parser.add_argument("--optimize-mode", default="quick",
                       choices=["quick", "standard", "comprehensive"],
                       help="Chế độ tối ưu hóa")
    parser.add_argument("--no-performance", action="store_true",
                       help="Tắt theo dõi hiệu suất")
    parser.add_argument("--config", default="config/config.yaml",
                       help="Đường dẫn file cấu hình")
    
    args = parser.parse_args()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Print banner
    print_startup_banner()
    print(f"🕐 Thời gian khởi động: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Chế độ: {args.mode.upper()}")
    
    try:
        # Load configuration
        config = load_config(args.config)
        logger.info("✅ Configuration loaded successfully")
        
        # Run based on mode
        if args.mode == "live":
            print("\n🔴 LIVE TRADING MODE - Giao dịch thực")
            asyncio.run(run_enhanced_bot(
                config, 
                demo_mode=False, 
                performance_tracking=not args.no_performance
            ))
            
        elif args.mode == "demo":
            print("\n🟡 DEMO MODE - Giao dịch thử nghiệm")
            asyncio.run(run_enhanced_bot(
                config, 
                demo_mode=True, 
                performance_tracking=not args.no_performance
            ))
            
        elif args.mode == "backtest":
            asyncio.run(run_backtest_mode(config, args.period, args.balance))
            
        elif args.mode == "optimize":
            asyncio.run(run_optimization_mode(config, args.optimize_mode))
        
        print("\n✅ Bot đã hoàn thành thành công!")
        
    except Exception as e:
        logger.error(f"❌ Failed to start enhanced trading bot: {e}")
        print(f"\n❌ Lỗi: {e}")
        print("\n💡 Kiểm tra:")
        print("   1. MT5 đã được cài đặt và đăng nhập")
        print("   2. File cấu hình config/config.yaml tồn tại")
        print("   3. Tất cả dependencies đã được cài đặt")
        sys.exit(1)


if __name__ == "__main__":
    main()

# MT5 Python Trading Bot Configuration
# Copy this file to config.yaml and update with your settings

# MetaTrader 5 Connection Settings
mt5:
  login: ********  # Your MT5 account number
  password: "your_password"  # Your MT5 password
  server: "YourBroker-Demo"  # Your broker's server name
  symbol: "XAUUSD"  # Trading symbol (Gold)
  magic_number: 234000  # Unique identifier for bot trades

# Trading Parameters
trading:
  timeframe: 5  # Timeframe in minutes (5 = M5)
  max_positions: 3  # Maximum concurrent positions
  max_daily_trades: 10  # Maximum trades per day
  risk_per_trade: 0.02  # Risk 2% of account per trade
  min_volume: 0.01  # Minimum lot size
  max_volume: 1.0  # Maximum lot size
  volume_step: 0.01  # Volume increment step
  contract_size: 100  # Contract size for calculations
  default_volume: 0.01  # Default volume for signals

# Technical Indicators Settings
indicators:
  macd_fast: 12  # MACD fast EMA period
  macd_slow: 26  # MACD slow EMA period
  macd_signal: 9  # MACD signal line EMA period
  atr_period: 14  # ATR calculation period
  pivot_method: "standard"  # Pivot points method: standard, fi<PERSON><PERSON><PERSON>, camarilla
  min_data_length: 50  # Minimum data length for indicator calculation
  min_data_length_atr: 20  # Minimum data length for ATR calculation
  lookback_period: 100  # Strategy lookback period
  trend_period: 20  # Period for trend calculation
  feature_size: 20  # AI feature vector size

# Risk Management
risk:
  max_drawdown: 0.10  # Maximum 10% drawdown
  stop_loss_atr_multiplier: 2.0  # Stop loss = ATR * 2.0
  take_profit_atr_multiplier: 3.0  # Take profit = ATR * 3.0
  trailing_stop: true  # Enable trailing stop
  trailing_stop_distance: 50  # Trailing stop distance in points
  max_spread: 30  # Maximum allowed spread in points
  margin_level_min: 200  # Minimum margin level percentage
  max_total_risk_exposure: 0.10  # Maximum 10% total risk exposure
  volatility_thresholds:
    low: 0.5  # Low volatility threshold
    medium: 1.0  # Medium volatility threshold
    high: 2.0  # High volatility threshold

# Strategy Scoring
strategy:
  scoring:
    macd_crossover_bullish: 0.4  # Score for bullish MACD crossover
    macd_crossover_bearish: 0.4  # Score for bearish MACD crossover
    macd_trend_bullish: 0.2  # Score for bullish MACD trend
    macd_trend_bearish: 0.2  # Score for bearish MACD trend
    pivot_above: 0.2  # Score for price above pivot
    pivot_below: 0.2  # Score for price below pivot
    market_trend_bullish: 0.2  # Score for bullish market trend
    market_trend_bearish: 0.2  # Score for bearish market trend
    ai_prediction_weight: 0.5  # Weight for AI prediction confidence
    technical_weight: 0.7  # Weight for technical analysis when AI is enabled
    ai_weight: 0.3  # Weight for AI analysis when AI is enabled
    min_signal_strength: 0.6  # Minimum signal strength to generate trade
    min_ai_confidence: 0.7  # Minimum AI confidence threshold

# AI/ML Model Configuration
ai:
  model_type: "dqn"  # Model type: dqn, ppo, a2c
  learning_rate: 0.001  # Learning rate for neural network
  batch_size: 32  # Training batch size
  memory_size: 10000  # Experience replay memory size
  epsilon_start: 1.0  # Initial exploration rate
  epsilon_end: 0.01  # Final exploration rate
  epsilon_decay: 0.995  # Exploration decay rate
  target_update_frequency: 100  # Target network update frequency
  training_frequency: 4  # Training frequency (every N steps)

# Backtesting Configuration
backtest:
  start_date: "2023-01-01"  # Backtest start date
  end_date: "2024-01-01"  # Backtest end date
  initial_balance: 1000.0  # Starting balance for backtest
  commission: 0.0  # Commission per trade
  spread: 20  # Average spread in points
  data_count: 10000  # Number of bars to retrieve for backtest
  min_data_required: 1000  # Minimum data required for backtest
  progress_log_interval: 1000  # Log progress every N bars
  performance_thresholds:
    win_rate_good: 0.5  # Good win rate threshold
    profit_factor_good: 1.5  # Good profit factor threshold
    profit_factor_marginal: 1.0  # Marginal profit factor threshold
    drawdown_low: 0.1  # Low drawdown threshold
    drawdown_moderate: 0.2  # Moderate drawdown threshold
    sharpe_good: 1.0  # Good Sharpe ratio threshold
    sharpe_moderate: 0.5  # Moderate Sharpe ratio threshold
    max_drawdown_warning: 0.15  # Warning threshold for drawdown

# Data Management
data:
  cache_size: 10000  # Number of bars to cache
  min_data_length: 100  # Minimum data length for analysis
  default_count: 1000  # Default number of bars to retrieve
  volatility_simulation: 0.002  # Volatility for mock data simulation
  price_range:
    min: 1800  # Minimum price for mock data
    max: 2200  # Maximum price for mock data
  volume_range:
    min: 100  # Minimum volume for mock data
    max: 1000  # Maximum volume for mock data

# Mock Trading (for testing)
mock:
  initial_balance: 10000.0  # Initial balance for mock trading
  initial_equity: 10000.0  # Initial equity for mock trading
  base_price: 2000.0  # Base price for mock data
  price_volatility: 0.001  # Price volatility for mock data
  order_counter_start: 1000  # Starting order counter
  spread_default: 20  # Default spread in points
  volume_max: 100.0  # Maximum volume for mock trading
  leverage: 100  # Leverage for mock account

# Performance Analysis
performance:
  annualization_factor: 252  # Trading days per year
  intraday_factor: 24  # Hours per day
  timeframe_factor: 12  # 5-minute intervals per hour
  var_confidence: 0.95  # VaR confidence level
  max_trades_for_metrics: 20  # Number of recent trades to show

# General Settings
logging_level: "INFO"  # Logging level: DEBUG, INFO, WARNING, ERROR
enable_ai: true  # Enable AI/ML features
enable_backtesting: false  # Enable backtesting mode
live_trading: false  # Enable live trading (set to true for real trading)

# WARNING: Set live_trading to true only when you're ready for real trading!
# Always test thoroughly in demo mode first.

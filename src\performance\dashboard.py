"""
Performance Dashboard
Real-time performance monitoring dashboard for the trading bot
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
from pathlib import Path

from .performance_tracker import PerformanceTracker, PerformanceMetrics
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PerformanceDashboard:
    """Real-time performance dashboard"""

    def __init__(self, performance_tracker: PerformanceTracker):
        self.tracker = performance_tracker
        self.dashboard_data = {}

    def generate_dashboard_data(self) -> Dict:
        """Generate comprehensive dashboard data"""
        metrics = self.tracker.get_current_metrics()
        summary = self.tracker.get_performance_summary()

        dashboard_data = {
            'timestamp': datetime.now().isoformat(),
            'account_overview': self._get_account_overview(summary),
            'performance_metrics': self._get_performance_metrics(metrics),
            'risk_analysis': self._get_risk_analysis(metrics),
            'trade_analysis': self._get_trade_analysis(metrics),
            'time_analysis': self._get_time_analysis(metrics),
            'market_condition_analysis': self._get_market_condition_analysis(metrics),
            'charts_data': self._get_charts_data(),
            'alerts': self._get_performance_alerts(metrics)
        }

        self.dashboard_data = dashboard_data
        return dashboard_data

    def _get_account_overview(self, summary: Dict) -> Dict:
        """Get account overview section"""
        account = summary['account_info']

        return {
            'current_balance': f"${account['current_balance']:,.2f}",
            'initial_balance': f"${account['initial_balance']:,.2f}",
            'total_return': f"${account['total_return']:,.2f}",
            'total_return_pct': f"{account['total_return_pct']:+.2f}%",
            'peak_balance': f"${account['peak_balance']:,.2f}",
            'open_trades': summary['open_trades'],
            'status': self._get_account_status(account['total_return_pct'])
        }

    def _get_performance_metrics(self, metrics: PerformanceMetrics) -> Dict:
        """Get performance metrics section"""
        return {
            'total_trades': metrics.total_trades,
            'win_rate': f"{metrics.win_rate:.1%}",
            'profit_factor': f"{metrics.profit_factor:.2f}",
            'expectancy': f"${metrics.expectancy:.2f}",
            'avg_win': f"${metrics.avg_win:.2f}",
            'avg_loss': f"${metrics.avg_loss:.2f}",
            'largest_win': f"${metrics.largest_win:.2f}",
            'largest_loss': f"${metrics.largest_loss:.2f}",
            'avg_trade_duration': f"{metrics.avg_trade_duration:.1f} min",
            'performance_rating': self._get_performance_rating(metrics)
        }

    def _get_risk_analysis(self, metrics: PerformanceMetrics) -> Dict:
        """Get risk analysis section"""
        return {
            'max_drawdown': f"${metrics.max_drawdown:.2f}",
            'max_drawdown_pct': f"{metrics.max_drawdown_pct:.2%}",
            'current_drawdown': f"${metrics.current_drawdown:.2f}",
            'current_drawdown_pct': f"{metrics.current_drawdown_pct:.2%}",
            'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
            'sortino_ratio': f"{metrics.sortino_ratio:.2f}",
            'calmar_ratio': f"{metrics.calmar_ratio:.2f}",
            'var_95': f"{metrics.var_95:.4f}",
            'kelly_criterion': f"{metrics.kelly_criterion:.2%}",
            'recovery_factor': f"{metrics.recovery_factor:.2f}",
            'risk_level': self._get_risk_level(metrics)
        }

    def _get_trade_analysis(self, metrics: PerformanceMetrics) -> Dict:
        """Get trade analysis section"""
        return {
            'winning_trades': metrics.winning_trades,
            'losing_trades': metrics.losing_trades,
            'max_consecutive_wins': metrics.max_consecutive_wins,
            'max_consecutive_losses': metrics.max_consecutive_losses,
            'current_consecutive_wins': metrics.current_consecutive_wins,
            'current_consecutive_losses': metrics.current_consecutive_losses,
            'win_loss_ratio': f"{metrics.avg_win / abs(metrics.avg_loss):.2f}" if metrics.avg_loss != 0 else "∞",
            'trade_frequency': self._calculate_trade_frequency(),
            'consistency_score': self._calculate_consistency_score(metrics)
        }

    def _get_time_analysis(self, metrics: PerformanceMetrics) -> Dict:
        """Get time-based analysis section"""
        return {
            'daily_return': f"{metrics.daily_return:.2%}",
            'weekly_return': f"{metrics.weekly_return:.2%}",
            'monthly_return': f"{metrics.monthly_return:.2%}",
            'annualized_return': f"{self._calculate_annualized_return():.2%}",
            'best_trading_day': self._get_best_trading_day(),
            'worst_trading_day': self._get_worst_trading_day(),
            'trading_days': self._get_trading_days_count()
        }

    def _get_market_condition_analysis(self, metrics: PerformanceMetrics) -> Dict:
        """Get market condition analysis section"""
        trending = metrics.trending_performance
        ranging = metrics.ranging_performance

        return {
            'trending_markets': {
                'trades': trending.get('trades', 0),
                'win_rate': f"{trending.get('win_rate', 0):.1%}",
                'avg_pnl': f"${trending.get('avg_pnl', 0):.2f}",
                'total_pnl': f"${trending.get('total_pnl', 0):.2f}"
            },
            'ranging_markets': {
                'trades': ranging.get('trades', 0),
                'win_rate': f"{ranging.get('win_rate', 0):.1%}",
                'avg_pnl': f"${ranging.get('avg_pnl', 0):.2f}",
                'total_pnl': f"${ranging.get('total_pnl', 0):.2f}"
            },
            'preferred_market': self._get_preferred_market_condition(trending, ranging)
        }

    def _get_charts_data(self) -> Dict:
        """Get data for charts and graphs"""
        equity_curve = []
        drawdown_curve = []

        for point in self.tracker.equity_curve:
            timestamp = point['timestamp']
            if hasattr(timestamp, 'isoformat'):
                timestamp_str = timestamp.isoformat()
            else:
                timestamp_str = str(timestamp)

            equity_curve.append({
                'timestamp': timestamp_str,
                'balance': point['balance']
            })

        # Calculate drawdown curve
        peak = self.tracker.initial_balance
        for point in self.tracker.equity_curve:
            if point['balance'] > peak:
                peak = point['balance']

            drawdown = (peak - point['balance']) / peak * 100
            timestamp = point['timestamp']
            if hasattr(timestamp, 'isoformat'):
                timestamp_str = timestamp.isoformat()
            else:
                timestamp_str = str(timestamp)

            drawdown_curve.append({
                'timestamp': timestamp_str,
                'drawdown': drawdown
            })

        # Monthly P&L
        monthly_pnl = self._calculate_monthly_pnl()

        # Trade distribution
        trade_distribution = self._get_trade_distribution()

        return {
            'equity_curve': equity_curve,
            'drawdown_curve': drawdown_curve,
            'monthly_pnl': monthly_pnl,
            'trade_distribution': trade_distribution,
            'win_loss_distribution': self._get_win_loss_distribution()
        }

    def _get_performance_alerts(self, metrics: PerformanceMetrics) -> List[Dict]:
        """Get performance alerts and warnings"""
        alerts = []

        # Drawdown alerts
        if metrics.current_drawdown_pct > 0.15:
            alerts.append({
                'type': 'danger',
                'title': 'High Drawdown Alert',
                'message': f"Current drawdown is {metrics.current_drawdown_pct:.1%}, consider reducing risk"
            })
        elif metrics.current_drawdown_pct > 0.10:
            alerts.append({
                'type': 'warning',
                'title': 'Moderate Drawdown',
                'message': f"Current drawdown is {metrics.current_drawdown_pct:.1%}, monitor closely"
            })

        # Consecutive losses alert
        if metrics.current_consecutive_losses >= 5:
            alerts.append({
                'type': 'warning',
                'title': 'Consecutive Losses',
                'message': f"{metrics.current_consecutive_losses} consecutive losses detected"
            })

        # Low win rate alert
        if metrics.win_rate < 0.4 and metrics.total_trades > 10:
            alerts.append({
                'type': 'warning',
                'title': 'Low Win Rate',
                'message': f"Win rate is {metrics.win_rate:.1%}, review strategy"
            })

        # Poor profit factor alert
        if metrics.profit_factor < 1.0 and metrics.total_trades > 5:
            alerts.append({
                'type': 'danger',
                'title': 'Negative Profit Factor',
                'message': f"Profit factor is {metrics.profit_factor:.2f}, strategy needs review"
            })

        # Positive alerts
        if metrics.current_consecutive_wins >= 5:
            alerts.append({
                'type': 'success',
                'title': 'Winning Streak',
                'message': f"{metrics.current_consecutive_wins} consecutive wins!"
            })

        if metrics.win_rate > 0.7 and metrics.total_trades > 10:
            alerts.append({
                'type': 'success',
                'title': 'High Win Rate',
                'message': f"Excellent win rate of {metrics.win_rate:.1%}"
            })

        return alerts

    def _get_account_status(self, return_pct: float) -> str:
        """Get account status based on return"""
        if return_pct > 20:
            return "🚀 Excellent"
        elif return_pct > 10:
            return "📈 Very Good"
        elif return_pct > 5:
            return "✅ Good"
        elif return_pct > 0:
            return "🟢 Positive"
        elif return_pct > -5:
            return "🟡 Slight Loss"
        elif return_pct > -10:
            return "🟠 Moderate Loss"
        else:
            return "🔴 Significant Loss"

    def _get_performance_rating(self, metrics: PerformanceMetrics) -> str:
        """Get overall performance rating"""
        score = 0

        # Win rate (25 points)
        if metrics.win_rate >= 0.6:
            score += 25
        elif metrics.win_rate >= 0.5:
            score += 20
        elif metrics.win_rate >= 0.4:
            score += 15
        else:
            score += 10

        # Profit factor (25 points)
        if metrics.profit_factor >= 2.0:
            score += 25
        elif metrics.profit_factor >= 1.5:
            score += 20
        elif metrics.profit_factor >= 1.2:
            score += 15
        elif metrics.profit_factor >= 1.0:
            score += 10

        # Sharpe ratio (25 points)
        if metrics.sharpe_ratio >= 2.0:
            score += 25
        elif metrics.sharpe_ratio >= 1.5:
            score += 20
        elif metrics.sharpe_ratio >= 1.0:
            score += 15
        elif metrics.sharpe_ratio >= 0.5:
            score += 10

        # Drawdown (25 points)
        if metrics.max_drawdown_pct <= 0.05:
            score += 25
        elif metrics.max_drawdown_pct <= 0.10:
            score += 20
        elif metrics.max_drawdown_pct <= 0.15:
            score += 15
        elif metrics.max_drawdown_pct <= 0.20:
            score += 10

        # Convert to rating
        if score >= 90:
            return "A+ (Excellent)"
        elif score >= 80:
            return "A (Very Good)"
        elif score >= 70:
            return "B+ (Good)"
        elif score >= 60:
            return "B (Fair)"
        elif score >= 50:
            return "C (Poor)"
        else:
            return "D (Very Poor)"

    def _get_risk_level(self, metrics: PerformanceMetrics) -> str:
        """Get risk level assessment"""
        if metrics.max_drawdown_pct <= 0.05:
            return "🟢 Low Risk"
        elif metrics.max_drawdown_pct <= 0.10:
            return "🟡 Moderate Risk"
        elif metrics.max_drawdown_pct <= 0.20:
            return "🟠 High Risk"
        else:
            return "🔴 Very High Risk"

    def _calculate_trade_frequency(self) -> str:
        """Calculate trade frequency"""
        if not self.tracker.trades:
            return "No trades"

        first_trade = min(self.tracker.trades, key=lambda t: t.entry_time)
        last_trade = max(self.tracker.trades, key=lambda t: t.entry_time)

        days = (last_trade.entry_time - first_trade.entry_time).days + 1
        trades_per_day = len(self.tracker.trades) / days

        if trades_per_day >= 5:
            return f"{trades_per_day:.1f} trades/day (Very Active)"
        elif trades_per_day >= 2:
            return f"{trades_per_day:.1f} trades/day (Active)"
        elif trades_per_day >= 1:
            return f"{trades_per_day:.1f} trades/day (Moderate)"
        else:
            return f"{trades_per_day:.1f} trades/day (Conservative)"

    def _calculate_consistency_score(self, metrics: PerformanceMetrics) -> str:
        """Calculate consistency score"""
        if metrics.total_trades < 10:
            return "Insufficient data"

        # Base score on win rate stability and drawdown control
        win_rate_score = min(100, metrics.win_rate * 200)  # 50% win rate = 100 points
        drawdown_score = max(0, 100 - metrics.max_drawdown_pct * 500)  # 20% DD = 0 points

        consistency = (win_rate_score + drawdown_score) / 2

        if consistency >= 80:
            return f"{consistency:.0f}% (Excellent)"
        elif consistency >= 60:
            return f"{consistency:.0f}% (Good)"
        elif consistency >= 40:
            return f"{consistency:.0f}% (Fair)"
        else:
            return f"{consistency:.0f}% (Poor)"

    def _calculate_annualized_return(self) -> float:
        """Calculate annualized return"""
        if not self.tracker.daily_balances:
            return 0

        first_balance = self.tracker.daily_balances[0]['balance']
        current_balance = self.tracker.current_balance
        days = len(self.tracker.daily_balances)

        if days == 0 or first_balance == 0:
            return 0

        total_return = (current_balance / first_balance) - 1
        annualized = (1 + total_return) ** (365 / days) - 1

        return annualized

    def _get_best_trading_day(self) -> str:
        """Get best trading day"""
        if not self.tracker.trades:
            return "No data"

        daily_pnl = {}
        for trade in self.tracker.trades:
            day = trade.entry_time.date()
            if day not in daily_pnl:
                daily_pnl[day] = 0
            daily_pnl[day] += trade.pnl

        if not daily_pnl:
            return "No data"

        best_day = max(daily_pnl.keys(), key=lambda d: daily_pnl[d])
        best_pnl = daily_pnl[best_day]

        return f"{best_day} (${best_pnl:.2f})"

    def _get_worst_trading_day(self) -> str:
        """Get worst trading day"""
        if not self.tracker.trades:
            return "No data"

        daily_pnl = {}
        for trade in self.tracker.trades:
            day = trade.entry_time.date()
            if day not in daily_pnl:
                daily_pnl[day] = 0
            daily_pnl[day] += trade.pnl

        if not daily_pnl:
            return "No data"

        worst_day = min(daily_pnl.keys(), key=lambda d: daily_pnl[d])
        worst_pnl = daily_pnl[worst_day]

        return f"{worst_day} (${worst_pnl:.2f})"

    def _get_trading_days_count(self) -> int:
        """Get number of trading days"""
        if not self.tracker.trades:
            return 0

        trading_days = set()
        for trade in self.tracker.trades:
            trading_days.add(trade.entry_time.date())

        return len(trading_days)

    def _get_preferred_market_condition(self, trending: Dict, ranging: Dict) -> str:
        """Get preferred market condition"""
        trending_score = trending.get('avg_pnl', 0) * trending.get('win_rate', 0)
        ranging_score = ranging.get('avg_pnl', 0) * ranging.get('win_rate', 0)

        if trending_score > ranging_score:
            return "Trending Markets"
        elif ranging_score > trending_score:
            return "Ranging Markets"
        else:
            return "No Preference"

    def _calculate_monthly_pnl(self) -> List[Dict]:
        """Calculate monthly P&L"""
        monthly_data = {}

        for trade in self.tracker.trades:
            month_key = trade.entry_time.strftime('%Y-%m')
            if month_key not in monthly_data:
                monthly_data[month_key] = 0
            monthly_data[month_key] += trade.pnl

        return [
            {'month': month, 'pnl': pnl}
            for month, pnl in sorted(monthly_data.items())
        ]

    def _get_trade_distribution(self) -> Dict:
        """Get trade distribution by hour and day"""
        hour_dist = {}
        day_dist = {}

        for trade in self.tracker.trades:
            hour = trade.entry_time.hour
            day = trade.entry_time.strftime('%A')

            hour_dist[hour] = hour_dist.get(hour, 0) + 1
            day_dist[day] = day_dist.get(day, 0) + 1

        return {
            'by_hour': [{'hour': h, 'trades': c} for h, c in sorted(hour_dist.items())],
            'by_day': [{'day': d, 'trades': c} for d, c in day_dist.items()]
        }

    def _get_win_loss_distribution(self) -> Dict:
        """Get win/loss distribution"""
        wins = [trade.pnl for trade in self.tracker.trades if trade.pnl > 0]
        losses = [trade.pnl for trade in self.tracker.trades if trade.pnl < 0]

        # Create bins for distribution
        win_bins = np.histogram(wins, bins=10) if wins else ([], [])
        loss_bins = np.histogram(losses, bins=10) if losses else ([], [])

        return {
            'wins': {
                'counts': win_bins[0].tolist() if len(win_bins[0]) > 0 else [],
                'bins': win_bins[1].tolist() if len(win_bins[1]) > 0 else []
            },
            'losses': {
                'counts': loss_bins[0].tolist() if len(loss_bins[0]) > 0 else [],
                'bins': loss_bins[1].tolist() if len(loss_bins[1]) > 0 else []
            }
        }

    def export_dashboard_data(self, filename: Optional[str] = None) -> str:
        """Export dashboard data to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_dashboard_{timestamp}.json"

        filepath = Path("reports") / filename
        filepath.parent.mkdir(exist_ok=True)

        dashboard_data = self.generate_dashboard_data()

        with open(filepath, 'w') as f:
            json.dump(dashboard_data, f, indent=2)

        logger.info(f"Dashboard data exported to {filepath}")
        return str(filepath)

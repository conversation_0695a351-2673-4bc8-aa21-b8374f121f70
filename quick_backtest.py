#!/usr/bin/env python3
"""
Quick Backtest Tool - <PERSON><PERSON> dàng cấu hình và chạy backtest
Công cụ này cho phép bạn nhanh chóng thay đổi thời gian backtest mà không cần chỉnh sửa config.yaml
"""

import sys
import asyncio
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.backtesting.backtest_engine import BacktestEngine, BacktestReporter
from src.core.mt5_client import MT5Client
from src.data.data_manager import DataManager
from src.utils.config import load_config
from src.utils.logger import setup_logger

logger = setup_logger()


def parse_date(date_str):
    """Parse date string in various formats"""
    formats = [
        '%Y-%m-%d',      # 2024-06-01
        '%d/%m/%Y',      # 01/06/2024
        '%d-%m-%Y',      # 01-06-2024
        '%Y/%m/%d',      # 2024/06/01
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    raise ValueError(f"Không thể parse ngày '{date_str}'. Hãy sử dụng format: YYYY-MM-DD")


def get_date_presets():
    """Get common date presets"""
    now = datetime.now()
    
    return {
        'last_week': (now - timedelta(days=7), now),
        'last_month': (now - timedelta(days=30), now),
        'last_3months': (now - timedelta(days=90), now),
        'last_6months': (now - timedelta(days=180), now),
        'last_year': (now - timedelta(days=365), now),
        'ytd': (datetime(now.year, 1, 1), now),  # Year to date
        'q1_2024': (datetime(2024, 1, 1), datetime(2024, 3, 31)),
        'q2_2024': (datetime(2024, 4, 1), datetime(2024, 6, 30)),
        'q3_2024': (datetime(2024, 7, 1), datetime(2024, 9, 30)),
        'q4_2024': (datetime(2024, 10, 1), datetime(2024, 12, 31)),
    }


async def run_quick_backtest(start_date, end_date, initial_balance=None):
    """Run backtest with specified parameters"""
    print("🤖 MT5 Gold Trading Bot - Quick Backtest")
    print("=" * 60)
    print(f"📅 Backtest Period: {start_date.date()} to {end_date.date()}")
    
    try:
        # Load configuration
        config = load_config()
        
        # Override backtest dates
        config.backtest.start_date = start_date.strftime('%Y-%m-%d')
        config.backtest.end_date = end_date.strftime('%Y-%m-%d')
        
        if initial_balance:
            config.backtest.initial_balance = initial_balance
            print(f"💰 Initial Balance: ${initial_balance:,.2f}")
        
        logger.info("Configuration loaded and updated")

        # Initialize MT5 client for data collection
        mt5_client = MT5Client(
            config.mt5.login,
            config.mt5.password,
            config.mt5.server,
            symbol=config.mt5.symbol,
            magic_number=config.mt5.magic_number
        )

        # Connect to MT5
        if not mt5_client.connect():
            logger.error("Failed to connect to MT5 for data collection")
            print("❌ Cannot connect to MT5. Please ensure:")
            print("   - MT5 terminal is running")
            print("   - Credentials in config.yaml are correct")
            print("   - Internet connection is stable")
            return False

        print("✅ Connected to MT5")

        # Initialize data manager
        data_manager = DataManager(mt5_client)

        # Calculate required data period (add buffer for indicators)
        data_start = start_date - timedelta(days=30)  # 30 days buffer for indicators
        days_needed = (datetime.now() - data_start).days + 10

        print(f"\n📊 Collecting data from {data_start.date()}...")

        # Collect historical data
        success = data_manager.collect_historical_data(
            symbol=config.mt5.symbol,
            days_back=days_needed
        )

        if not success:
            print("❌ Failed to collect historical data")
            return False

        # Get market data
        data = data_manager.get_market_data(
            symbol=config.mt5.symbol,
            timeframe=5,
            count=50000,
            use_cache=False
        )

        if data is None or len(data) < 100:
            print("❌ Insufficient data for backtesting")
            return False

        print(f"✅ Data ready: {len(data)} bars from {data.index[0]} to {data.index[-1]}")

        # Validate date range
        if start_date < data.index[0]:
            print(f"⚠️  Start date {start_date.date()} is before available data {data.index[0].date()}")
            start_date = data.index[0]
            print(f"   Adjusted to: {start_date.date()}")

        if end_date > data.index[-1]:
            print(f"⚠️  End date {end_date.date()} is after available data {data.index[-1].date()}")
            end_date = data.index[-1]
            print(f"   Adjusted to: {end_date.date()}")

        print(f"\n🔄 Running backtest from {start_date.date()} to {end_date.date()}")

        # Initialize and run backtest
        backtest_engine = BacktestEngine(config)
        results = backtest_engine.run_backtest(
            data=data,
            start_date=start_date,
            end_date=end_date
        )

        # Display results
        print("\n" + "=" * 60)
        print("📈 BACKTEST RESULTS")
        print("=" * 60)

        print(f"📅 Period: {results.start_date.date()} to {results.end_date.date()}")
        print(f"💰 Initial Balance: ${results.initial_balance:,.2f}")
        print(f"💰 Final Balance: ${results.final_balance:,.2f}")
        print(f"📊 Total Return: {results.total_return_pct:.2%} (${results.total_return:,.2f})")
        print(f"📉 Max Drawdown: {results.max_drawdown_pct:.2%}")
        print(f"🎯 Total Trades: {results.total_trades}")
        print(f"🏆 Win Rate: {results.win_rate:.1%}")
        print(f"⚖️  Profit Factor: {results.profit_factor:.2f}")
        print(f"📊 Sharpe Ratio: {results.sharpe_ratio:.2f}")

        # Generate report
        print("\n📝 Generating report...")
        reporter = BacktestReporter()
        report_path = reporter.generate_report(results)
        print(f"✅ Report saved: {report_path}")

        # Disconnect
        mt5_client.disconnect()
        print(f"\n✅ Backtest completed successfully!")
        
        return True

    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        print(f"❌ Backtest failed: {e}")
        return False


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="Quick Backtest Tool - Dễ dàng cấu hình thời gian backtest",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ví dụ sử dụng:

1. Backtest tháng trước:
   python quick_backtest.py --preset last_month

2. Backtest khoảng thời gian cụ thể:
   python quick_backtest.py --start 2024-06-01 --end 2024-07-01

3. Backtest với số dư khác:
   python quick_backtest.py --preset last_week --balance 1000

4. Xem các preset có sẵn:
   python quick_backtest.py --list-presets

Các preset có sẵn: last_week, last_month, last_3months, last_6months, 
last_year, ytd, q1_2024, q2_2024, q3_2024, q4_2024
        """
    )
    
    parser.add_argument('--start', type=str, help='Ngày bắt đầu (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, help='Ngày kết thúc (YYYY-MM-DD)')
    parser.add_argument('--preset', type=str, help='Sử dụng preset thời gian có sẵn')
    parser.add_argument('--balance', type=float, help='Số dư ban đầu')
    parser.add_argument('--list-presets', action='store_true', help='Hiển thị các preset có sẵn')
    
    args = parser.parse_args()
    
    # List presets
    if args.list_presets:
        presets = get_date_presets()
        print("📅 Các preset thời gian có sẵn:")
        print("-" * 50)
        for name, (start, end) in presets.items():
            print(f"  {name:<15} : {start.date()} to {end.date()}")
        return
    
    # Determine start and end dates
    start_date = None
    end_date = None
    
    if args.preset:
        presets = get_date_presets()
        if args.preset not in presets:
            print(f"❌ Preset '{args.preset}' không tồn tại.")
            print("Sử dụng --list-presets để xem các preset có sẵn.")
            return
        start_date, end_date = presets[args.preset]
        
    elif args.start and args.end:
        try:
            start_date = parse_date(args.start)
            end_date = parse_date(args.end)
        except ValueError as e:
            print(f"❌ {e}")
            return
            
    else:
        print("❌ Vui lòng chỉ định thời gian bằng --preset hoặc --start và --end")
        print("Sử dụng --help để xem hướng dẫn chi tiết.")
        return
    
    # Validate dates
    if start_date >= end_date:
        print("❌ Ngày bắt đầu phải trước ngày kết thúc")
        return
    
    # Run backtest
    success = asyncio.run(run_quick_backtest(start_date, end_date, args.balance))
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

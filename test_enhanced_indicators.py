#!/usr/bin/env python3
"""
Test Enhanced Technical Indicators
Quick test to verify the new indicators are working correctly
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.indicators.technical_indicators import TechnicalIndicators
from src.utils.config import load_config

def create_sample_data(periods=100):
    """Create sample OHLC data for testing"""
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 2000
    dates = pd.date_range(start='2024-01-01', periods=periods, freq='5T')
    
    # Generate price movements
    returns = np.random.normal(0, 0.001, periods)
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # Create OHLC data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.0005)))
        low = price * (1 - abs(np.random.normal(0, 0.0005)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        
        data.append({
            'open': open_price,
            'high': max(high, open_price, close_price),
            'low': min(low, open_price, close_price),
            'close': close_price,
            'volume': np.random.randint(100, 1000)
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_enhanced_indicators():
    """Test all enhanced indicators"""
    print("🧪 Testing Enhanced Technical Indicators")
    print("=" * 50)
    
    try:
        # Load config
        config = load_config()
        
        # Initialize indicators
        indicators = TechnicalIndicators(config)
        
        # Create sample data
        data = create_sample_data(100)
        print(f"✅ Created sample data: {len(data)} bars")
        
        # Test individual indicators
        print("\n📊 Testing Individual Indicators:")
        
        # MACD
        macd = indicators.calculate_macd(data)
        print(f"MACD: {macd.macd:.4f}, Signal: {macd.signal:.4f}, Trend: {macd.trend}, Crossover: {macd.crossover}")
        
        # RSI
        rsi = indicators.calculate_rsi(data)
        print(f"RSI: {rsi.rsi:.2f}, Condition: {rsi.condition}, Divergence: {rsi.divergence}")
        
        # Bollinger Bands
        bb = indicators.calculate_bollinger_bands(data)
        print(f"BB Upper: {bb.upper:.2f}, Middle: {bb.middle:.2f}, Lower: {bb.lower:.2f}")
        print(f"BB Position: {bb.position}, Squeeze: {bb.squeeze}")
        
        # ATR
        atr = indicators.calculate_atr(data)
        print(f"ATR: {atr.atr:.4f}, Percentage: {atr.atr_percentage:.2f}%, Volatility: {atr.volatility_level}")
        
        # Market Regime
        regime = indicators.detect_market_regime(data)
        print(f"Market Regime - Direction: {regime.trend_direction}, Strength: {regime.trend_strength:.2f}")
        print(f"Volatility Regime: {regime.volatility_regime}, Phase: {regime.market_phase}")
        
        # Signal Confirmations
        buy_conf = indicators.get_signal_confirmation(data, 'buy')
        sell_conf = indicators.get_signal_confirmation(data, 'sell')
        print(f"Buy Confirmation: {buy_conf.overall_confidence:.2f}")
        print(f"Sell Confirmation: {sell_conf.overall_confidence:.2f}")
        
        # Combined Signal
        print("\n🎯 Testing Combined Signal:")
        combined = indicators.get_combined_signal(data)
        
        print(f"Final Recommendation: {combined['final_recommendation']}")
        print(f"Signal Strength: {combined['signal_strength']:.3f}")
        print(f"Market Phase: {combined['market_regime']['market_phase']}")
        print(f"Buy Confidence: {combined['signal_confirmations']['buy_confirmation']['overall_confidence']:.2f}")
        print(f"Sell Confidence: {combined['signal_confirmations']['sell_confirmation']['overall_confidence']:.2f}")
        
        print("\n✅ All enhanced indicators working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing indicators: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_indicators()
    sys.exit(0 if success else 1)

# Setup Guide - MT5 Trading Bot

## 🚀 Quick Installation

### Automatic Setup (Recommended)
```bash
# Clone repository
git clone https://github.com/your-username/mt5-python-trading-bot.git
cd mt5-python-trading-bot

# Run automatic setup
python setup_venv.py
```

### Manual Setup

#### 1. Prerequisites
- **Python 3.8+** (check: `python --version`)
- **MetaTrader 5** (Windows only, for live trading)
- **MT5 Account** with API access enabled

#### 2. Create Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate (choose your OS)
source venv/bin/activate    # macOS/Linux
venv\Scripts\activate       # Windows
```

#### 3. Install Dependencies
```bash
# Update pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# For macOS users (if TA-Lib issues)
pip install -r requirements-macos.txt
```

#### 4. Configure Bot
```bash
# Copy configuration template
cp config/config.example.yaml config/config.yaml

# Edit with your MT5 credentials
nano config/config.yaml     # Linux/macOS
notepad config/config.yaml  # Windows
```

#### 5. Test Setup
```bash
python quick_start.py
```

## 🔧 Platform-Specific Setup

### Windows (Live Trading)
- Install MetaTrader 5 terminal
- Enable API access in MT5 settings
- Configure `config.yaml` with real MT5 credentials
- Set `live_trading: true` for real trading

### macOS/Linux (Development/Testing)
- Uses mock MT5 client for development
- Real market data from Yahoo Finance
- Full backtesting capabilities
- Strategy development and testing

**Note**: MetaTrader 5 only runs on Windows. macOS/Linux users get:
- ✅ Real market data
- ✅ Technical indicators
- ✅ Backtesting
- ✅ Strategy development
- 🔄 Mock trading (no real money)

## 📋 Configuration

### Basic MT5 Settings
```yaml
mt5:
  login: ********          # Your MT5 account number
  password: "your_password" # Your MT5 password
  server: "YourBroker-Demo" # Your broker's server
  symbol: "XAUUSD"         # Trading symbol (Gold)
  magic_number: 234000     # Unique identifier

# IMPORTANT: Set to false for demo mode
live_trading: false        # false = demo, true = live
```

### Trading Parameters
```yaml
trading:
  timeframe: 5             # 5-minute timeframe
  max_positions: 3         # Maximum concurrent positions
  max_daily_trades: 10     # Daily trade limit
  risk_per_trade: 0.02     # 2% risk per trade
  min_volume: 0.01         # Minimum lot size
  max_volume: 1.0          # Maximum lot size
```

### Risk Management
```yaml
risk:
  max_drawdown: 0.10       # 10% maximum drawdown
  stop_loss_atr_multiplier: 2.0   # SL = ATR * 2.0
  take_profit_atr_multiplier: 3.0 # TP = ATR * 3.0
  trailing_stop: true      # Enable trailing stops
  max_spread: 30           # Maximum spread in points
```

## 🛠️ Troubleshooting

### Common Issues

#### "python: command not found"
```bash
# Try these commands:
python3 --version
py --version        # Windows
python3.8 --version

# Use the command that works:
python3 setup_venv.py
```

#### "No module named venv"
```bash
# Ubuntu/Debian:
sudo apt-get install python3-venv

# Or use virtualenv:
pip install virtualenv
virtualenv venv
```

#### TA-Lib Installation Issues (macOS)
```bash
# Install TA-Lib via Homebrew
brew install ta-lib

# Set environment variables
export TA_INCLUDE_PATH=$(brew --prefix ta-lib)/include
export TA_LIBRARY_PATH=$(brew --prefix ta-lib)/lib
pip install TA-Lib
```

#### Virtual Environment Not Activating
```bash
# Check if files exist
ls venv/bin/activate      # macOS/Linux
dir venv\Scripts\activate.bat  # Windows

# Activate directly
bash venv/bin/activate    # macOS/Linux
```

## 🎯 Usage Commands

```bash
# Activate virtual environment
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows

# Run bot
python main.py

# Run tests
python run_tests.py

# Run backtest
python run_backtest.py

# Quick validation
python quick_start.py

# Deactivate when done
deactivate
```

## ⚠️ Safety First

### Before Live Trading:
1. ✅ Test thoroughly in demo mode
2. ✅ Run backtests with historical data
3. ✅ Understand risk parameters
4. ✅ Start with small amounts
5. ✅ Monitor regularly

### Safe Configuration:
```yaml
trading:
  risk_per_trade: 0.01     # Only 1% risk per trade
  max_positions: 1         # Only 1 position at a time
  max_daily_trades: 3      # Max 3 trades per day

risk:
  max_drawdown: 0.05       # Stop at 5% drawdown
```

## 📊 Understanding Backtest Results

After running `python run_backtest.py`:

```
📈 BACKTEST RESULTS
💰 Total Return: 5.23% ($523.45)
🏆 Win Rate: 65.2%
📉 Max Drawdown: 3.45%
⚖️ Profit Factor: 1.85
📊 Sharpe Ratio: 1.23
```

**Key Metrics:**
- **Total Return**: Overall profit/loss (%)
- **Win Rate**: Percentage of winning trades (>50% is good)
- **Max Drawdown**: Maximum loss from peak (lower is better)
- **Profit Factor**: Profit/Loss ratio (>1.5 is good)
- **Sharpe Ratio**: Risk-adjusted performance (>1.0 is good)
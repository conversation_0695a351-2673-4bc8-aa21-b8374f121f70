"""
Strategy Optimization Framework
Comprehensive testing and optimization of trading strategy parameters
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import json
from pathlib import Path

from ..backtesting.backtest_engine import BacktestEngine, SimpleBacktestConfig
from ..utils.config import load_config
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class OptimizationParameter:
    """Parameter for optimization"""
    name: str
    min_value: float
    max_value: float
    step: float
    current_value: float = None


@dataclass
class OptimizationResult:
    """Result of a single optimization run"""
    parameters: Dict[str, float]
    total_return_pct: float
    win_rate: float
    profit_factor: float
    max_drawdown_pct: float
    sharpe_ratio: float
    total_trades: int
    score: float  # Composite optimization score
    market_condition: str
    test_period: str


class StrategyOptimizer:
    """Strategy optimization and testing framework"""

    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            config_path = "config/config.yaml"
        self.config = load_config(config_path)
        self.optimization_results: List[OptimizationResult] = []
        self.best_parameters: Dict[str, Dict] = {}  # Best params by market condition

        # Default optimization parameters
        self.optimization_params = {
            'macd_fast': OptimizationParameter('macd_fast', 8, 16, 2),
            'macd_slow': OptimizationParameter('macd_slow', 20, 30, 2),
            'macd_signal': OptimizationParameter('macd_signal', 5, 12, 1),
            'rsi_period': OptimizationParameter('rsi_period', 10, 20, 2),
            'bb_period': OptimizationParameter('bb_period', 15, 25, 2),
            'signal_threshold': OptimizationParameter('signal_threshold', 0.5, 0.8, 0.05),
            'risk_per_trade': OptimizationParameter('risk_per_trade', 0.01, 0.03, 0.005),
            'stop_loss_atr_multiplier': OptimizationParameter('stop_loss_atr_multiplier', 1.5, 3.0, 0.25),
            'take_profit_atr_multiplier': OptimizationParameter('take_profit_atr_multiplier', 2.0, 4.0, 0.5)
        }

        # Test periods for different market conditions
        self.test_periods = {
            'trending_bull': ('2024-01-01', '2024-03-31'),
            'trending_bear': ('2024-04-01', '2024-06-30'),
            'ranging': ('2024-07-01', '2024-09-30'),
            'volatile': ('2024-10-01', '2024-12-31'),
            'full_year': ('2024-01-01', '2024-12-31')
        }

    def optimize_strategy(self, symbol: str = "XAUUSD",
                         max_combinations: int = 1000,
                         parallel_workers: int = 4) -> Dict:
        """
        Optimize strategy parameters across different market conditions

        Args:
            symbol: Trading symbol
            max_combinations: Maximum parameter combinations to test
            parallel_workers: Number of parallel workers for optimization

        Returns:
            Dictionary with optimization results
        """
        logger.info(f"Starting strategy optimization for {symbol}")
        logger.info(f"Testing up to {max_combinations} parameter combinations")

        # Generate parameter combinations
        param_combinations = self._generate_parameter_combinations(max_combinations)
        logger.info(f"Generated {len(param_combinations)} parameter combinations")

        # Test each combination across different market conditions
        all_results = []

        for market_condition, (start_date, end_date) in self.test_periods.items():
            logger.info(f"Testing {market_condition} period: {start_date} to {end_date}")

            # Test parameter combinations for this market condition
            condition_results = self._test_parameter_combinations(
                param_combinations, symbol, start_date, end_date,
                market_condition, parallel_workers
            )

            all_results.extend(condition_results)

            # Find best parameters for this market condition
            if condition_results:
                best_result = max(condition_results, key=lambda r: r.score)
                self.best_parameters[market_condition] = {
                    'parameters': best_result.parameters,
                    'score': best_result.score,
                    'metrics': {
                        'total_return_pct': best_result.total_return_pct,
                        'win_rate': best_result.win_rate,
                        'profit_factor': best_result.profit_factor,
                        'max_drawdown_pct': best_result.max_drawdown_pct,
                        'sharpe_ratio': best_result.sharpe_ratio
                    }
                }

                logger.info(f"Best {market_condition} score: {best_result.score:.3f}")

        self.optimization_results = all_results

        # Analyze results
        optimization_summary = self._analyze_optimization_results()

        # Save results
        self._save_optimization_results(optimization_summary)

        logger.info("Strategy optimization completed")
        return optimization_summary

    def _generate_parameter_combinations(self, max_combinations: int) -> List[Dict]:
        """Generate parameter combinations for testing"""
        param_ranges = {}

        for param_name, param_config in self.optimization_params.items():
            values = np.arange(
                param_config.min_value,
                param_config.max_value + param_config.step,
                param_config.step
            )
            param_ranges[param_name] = values

        # Generate all combinations
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())

        all_combinations = list(itertools.product(*param_values))

        # Limit combinations if too many
        if len(all_combinations) > max_combinations:
            # Use random sampling to get diverse combinations
            np.random.seed(42)
            selected_indices = np.random.choice(
                len(all_combinations), max_combinations, replace=False
            )
            all_combinations = [all_combinations[i] for i in selected_indices]

        # Convert to list of dictionaries
        combinations = []
        for combination in all_combinations:
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)

        return combinations

    def _test_parameter_combinations(self, combinations: List[Dict], symbol: str,
                                   start_date: str, end_date: str,
                                   market_condition: str, workers: int) -> List[OptimizationResult]:
        """Test parameter combinations for a specific market condition"""
        results = []

        # For now, run sequentially to avoid complexity
        # In production, this could be parallelized
        for i, params in enumerate(combinations):
            if i % 50 == 0:
                logger.info(f"Testing combination {i+1}/{len(combinations)} for {market_condition}")

            try:
                result = self._test_single_combination(
                    params, symbol, start_date, end_date, market_condition
                )
                if result:
                    results.append(result)
            except Exception as e:
                logger.warning(f"Failed to test combination {i+1}: {e}")
                continue

        return results

    def _test_single_combination(self, params: Dict, symbol: str,
                               start_date: str, end_date: str,
                               market_condition: str) -> Optional[OptimizationResult]:
        """Test a single parameter combination"""
        try:
            # Create modified config with test parameters
            test_config = self._create_test_config(params)

            # Create backtest engine with test config
            engine = BacktestEngine(test_config)

            # Create simple backtest config
            simple_config = SimpleBacktestConfig(
                start_date=start_date,
                end_date=end_date,
                initial_balance=10000.0
            )

            # Run backtest
            results = engine.run_simple_backtest(simple_config, symbol)

            # Calculate optimization score
            score = self._calculate_optimization_score(results)

            return OptimizationResult(
                parameters=params.copy(),
                total_return_pct=results.total_return_pct,
                win_rate=results.win_rate,
                profit_factor=results.profit_factor,
                max_drawdown_pct=results.max_drawdown_pct,
                sharpe_ratio=results.sharpe_ratio,
                total_trades=results.total_trades,
                score=score,
                market_condition=market_condition,
                test_period=f"{start_date} to {end_date}"
            )

        except Exception as e:
            logger.warning(f"Error testing parameters {params}: {e}")
            return None

    def _create_test_config(self, params: Dict):
        """Create test configuration with modified parameters"""
        # Create a copy of the base config
        test_config = self.config

        # Update indicator parameters
        if hasattr(test_config, 'indicators'):
            test_config.indicators.macd_fast = int(params.get('macd_fast', 12))
            test_config.indicators.macd_slow = int(params.get('macd_slow', 26))
            test_config.indicators.macd_signal = int(params.get('macd_signal', 9))

        # Update strategy parameters
        if hasattr(test_config, 'strategy_scoring'):
            test_config.strategy_scoring.min_signal_strength = params.get('signal_threshold', 0.6)

        # Update risk parameters
        if hasattr(test_config, 'trading'):
            test_config.trading.risk_per_trade = params.get('risk_per_trade', 0.02)

        if hasattr(test_config, 'risk'):
            test_config.risk.stop_loss_atr_multiplier = params.get('stop_loss_atr_multiplier', 2.0)
            test_config.risk.take_profit_atr_multiplier = params.get('take_profit_atr_multiplier', 3.0)

        return test_config

    def _calculate_optimization_score(self, results) -> float:
        """Calculate composite optimization score"""
        # Weighted scoring system
        weights = {
            'return': 0.3,      # 30% weight on returns
            'win_rate': 0.2,    # 20% weight on win rate
            'profit_factor': 0.2, # 20% weight on profit factor
            'sharpe': 0.15,     # 15% weight on Sharpe ratio
            'drawdown': 0.15    # 15% weight on drawdown (inverted)
        }

        # Normalize metrics (0-1 scale)
        return_score = min(1.0, max(0.0, results.total_return_pct / 50))  # 50% return = max score
        win_rate_score = results.win_rate
        profit_factor_score = min(1.0, max(0.0, (results.profit_factor - 1) / 2))  # PF of 3 = max score
        sharpe_score = min(1.0, max(0.0, results.sharpe_ratio / 3))  # Sharpe of 3 = max score
        drawdown_score = max(0.0, 1.0 - (results.max_drawdown_pct / 0.3))  # 30% DD = 0 score

        # Calculate weighted score
        score = (
            weights['return'] * return_score +
            weights['win_rate'] * win_rate_score +
            weights['profit_factor'] * profit_factor_score +
            weights['sharpe'] * sharpe_score +
            weights['drawdown'] * drawdown_score
        )

        # Penalty for insufficient trades
        if results.total_trades < 10:
            score *= 0.5

        return score

    def _analyze_optimization_results(self) -> Dict:
        """Analyze optimization results and generate summary"""
        if not self.optimization_results:
            return {'error': 'No optimization results available'}

        # Overall best parameters (across all market conditions)
        overall_best = max(self.optimization_results, key=lambda r: r.score)

        # Best parameters by market condition
        condition_analysis = {}
        for condition in self.test_periods.keys():
            condition_results = [r for r in self.optimization_results if r.market_condition == condition]
            if condition_results:
                best_for_condition = max(condition_results, key=lambda r: r.score)
                condition_analysis[condition] = {
                    'best_score': best_for_condition.score,
                    'best_parameters': best_for_condition.parameters,
                    'metrics': {
                        'total_return_pct': best_for_condition.total_return_pct,
                        'win_rate': best_for_condition.win_rate,
                        'profit_factor': best_for_condition.profit_factor,
                        'max_drawdown_pct': best_for_condition.max_drawdown_pct,
                        'sharpe_ratio': best_for_condition.sharpe_ratio,
                        'total_trades': best_for_condition.total_trades
                    },
                    'total_tests': len(condition_results)
                }

        # Parameter sensitivity analysis
        param_sensitivity = self._analyze_parameter_sensitivity()

        # Performance distribution
        scores = [r.score for r in self.optimization_results]
        returns = [r.total_return_pct for r in self.optimization_results]

        summary = {
            'optimization_summary': {
                'total_combinations_tested': len(self.optimization_results),
                'best_overall_score': overall_best.score,
                'best_overall_parameters': overall_best.parameters,
                'best_overall_return': overall_best.total_return_pct,
                'score_statistics': {
                    'mean': np.mean(scores),
                    'std': np.std(scores),
                    'min': np.min(scores),
                    'max': np.max(scores),
                    'percentile_75': np.percentile(scores, 75),
                    'percentile_90': np.percentile(scores, 90)
                },
                'return_statistics': {
                    'mean': np.mean(returns),
                    'std': np.std(returns),
                    'min': np.min(returns),
                    'max': np.max(returns),
                    'positive_returns_pct': len([r for r in returns if r > 0]) / len(returns) * 100
                }
            },
            'market_condition_analysis': condition_analysis,
            'parameter_sensitivity': param_sensitivity,
            'recommendations': self._generate_recommendations(),
            'timestamp': datetime.now().isoformat()
        }

        return summary

    def _analyze_parameter_sensitivity(self) -> Dict:
        """Analyze sensitivity of each parameter to performance"""
        sensitivity = {}

        # Get all unique parameter names from results
        all_param_names = set()
        for result in self.optimization_results:
            all_param_names.update(result.parameters.keys())

        for param_name in all_param_names:
            # Only analyze parameters that exist in all results
            param_values = []
            scores = []

            for result in self.optimization_results:
                if param_name in result.parameters:
                    param_values.append(result.parameters[param_name])
                    scores.append(result.score)

            if len(param_values) < 2:
                continue  # Skip if insufficient data

            # Calculate correlation between parameter value and score
            try:
                correlation = np.corrcoef(param_values, scores)[0, 1]
                if np.isnan(correlation):
                    correlation = 0.0
            except:
                correlation = 0.0

            # Find optimal range
            sorted_results = sorted(
                zip(param_values, scores),
                key=lambda x: x[1],
                reverse=True
            )

            # Top 10% of results
            top_10_percent = sorted_results[:max(1, len(sorted_results) // 10)]
            optimal_values = [result[0] for result in top_10_percent]

            if optimal_values:
                sensitivity[param_name] = {
                    'correlation_with_score': correlation,
                    'optimal_range': {
                        'min': min(optimal_values),
                        'max': max(optimal_values),
                        'mean': np.mean(optimal_values),
                        'std': np.std(optimal_values) if len(optimal_values) > 1 else 0.0
                    },
                    'current_range': {
                        'min': min(param_values),
                        'max': max(param_values)
                    },
                    'data_points': len(param_values)
                }

        return sensitivity

    def _generate_recommendations(self) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []

        if not self.optimization_results:
            return ["No optimization results available for recommendations"]

        # Overall performance recommendation
        overall_best = max(self.optimization_results, key=lambda r: r.score)
        if overall_best.score > 0.7:
            recommendations.append(f"✅ Excellent optimization results found with score {overall_best.score:.3f}")
        elif overall_best.score > 0.5:
            recommendations.append(f"⚡ Good optimization results found with score {overall_best.score:.3f}")
        else:
            recommendations.append(f"⚠️ Optimization results are modest with best score {overall_best.score:.3f}")

        # Market condition recommendations
        condition_scores = {}
        for condition in self.test_periods.keys():
            condition_results = [r for r in self.optimization_results if r.market_condition == condition]
            if condition_results:
                best_score = max(r.score for r in condition_results)
                condition_scores[condition] = best_score

        if condition_scores:
            best_condition = max(condition_scores.keys(), key=lambda k: condition_scores[k])
            worst_condition = min(condition_scores.keys(), key=lambda k: condition_scores[k])

            recommendations.append(f"📈 Strategy performs best in {best_condition} markets (score: {condition_scores[best_condition]:.3f})")
            recommendations.append(f"📉 Strategy struggles most in {worst_condition} markets (score: {condition_scores[worst_condition]:.3f})")

        # Parameter recommendations
        param_sensitivity = self._analyze_parameter_sensitivity()
        if param_sensitivity:
            most_sensitive = max(param_sensitivity.keys(),
                               key=lambda k: abs(param_sensitivity[k]['correlation_with_score']))

            recommendations.append(f"🎯 Most sensitive parameter: {most_sensitive} (correlation: {param_sensitivity[most_sensitive]['correlation_with_score']:.3f})")
        else:
            recommendations.append("📊 Insufficient parameter data for sensitivity analysis")

        # Risk recommendations
        high_return_results = [r for r in self.optimization_results if r.total_return_pct > 20]
        if high_return_results:
            avg_drawdown = np.mean([r.max_drawdown_pct for r in high_return_results])
            if avg_drawdown > 0.15:
                recommendations.append("⚠️ High-return configurations tend to have high drawdowns - consider risk management")
            else:
                recommendations.append("✅ High-return configurations maintain reasonable drawdowns")

        return recommendations

    def _save_optimization_results(self, summary: Dict) -> None:
        """Save optimization results to file"""
        try:
            # Create reports directory
            reports_dir = Path("reports/optimization")
            reports_dir.mkdir(parents=True, exist_ok=True)

            # Save summary
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = reports_dir / f"optimization_summary_{timestamp}.json"

            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)

            # Save detailed results
            detailed_file = reports_dir / f"optimization_detailed_{timestamp}.json"
            detailed_results = [
                {
                    'parameters': r.parameters,
                    'metrics': {
                        'total_return_pct': r.total_return_pct,
                        'win_rate': r.win_rate,
                        'profit_factor': r.profit_factor,
                        'max_drawdown_pct': r.max_drawdown_pct,
                        'sharpe_ratio': r.sharpe_ratio,
                        'total_trades': r.total_trades,
                        'score': r.score
                    },
                    'market_condition': r.market_condition,
                    'test_period': r.test_period
                }
                for r in self.optimization_results
            ]

            with open(detailed_file, 'w') as f:
                json.dump(detailed_results, f, indent=2)

            logger.info(f"Optimization results saved to {summary_file} and {detailed_file}")

        except Exception as e:
            logger.error(f"Failed to save optimization results: {e}")

    def get_recommended_parameters(self, market_condition: str = 'overall') -> Dict:
        """Get recommended parameters for a specific market condition"""
        if market_condition == 'overall':
            if self.optimization_results:
                best_result = max(self.optimization_results, key=lambda r: r.score)
                return best_result.parameters
            else:
                return {}
        else:
            return self.best_parameters.get(market_condition, {}).get('parameters', {})

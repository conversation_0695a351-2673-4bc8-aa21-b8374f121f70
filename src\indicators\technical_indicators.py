"""
Enhanced Technical Indicators for Gold Trading
Implements MACD, ATR, Pivot Points, RSI, Bollinger Bands, and advanced signal filtering
"""

import pandas as pd
import numpy as np
from typing import Tuple, Dict, Optional, List
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MACDSignal:
    """MACD signal data structure"""
    macd: float
    signal: float
    histogram: float
    trend: str  # 'bullish', 'bearish', 'neutral'
    crossover: str  # 'bullish_cross', 'bearish_cross', 'none'


@dataclass
class ATRData:
    """ATR data structure"""
    atr: float
    atr_percentage: float
    volatility_level: str  # 'low', 'medium', 'high'


@dataclass
class PivotPoints:
    """Pivot points data structure"""
    pivot: float
    r1: float
    r2: float
    r3: float
    s1: float
    s2: float
    s3: float
    current_level: str  # 'above_pivot', 'below_pivot', 'at_pivot'


@dataclass
class RSIData:
    """RSI data structure"""
    rsi: float
    condition: str  # 'oversold', 'overbought', 'neutral'
    divergence: str  # 'bullish', 'bearish', 'none'


@dataclass
class BollingerBands:
    """Bollinger Bands data structure"""
    upper: float
    middle: float
    lower: float
    position: str  # 'above_upper', 'below_lower', 'between_bands'
    squeeze: bool  # True if bands are contracting (low volatility)


@dataclass
class MarketRegime:
    """Market regime detection data structure"""
    trend_direction: str  # 'bullish', 'bearish', 'sideways'
    trend_strength: float  # 0-1, strength of the trend
    volatility_regime: str  # 'low', 'medium', 'high', 'extreme'
    market_phase: str  # 'trending', 'ranging', 'breakout', 'reversal'


@dataclass
class SignalConfirmation:
    """Signal confirmation data structure"""
    macd_confirmed: bool
    rsi_confirmed: bool
    bollinger_confirmed: bool
    volume_confirmed: bool
    trend_confirmed: bool
    overall_confidence: float  # 0-1, overall signal confidence


class TechnicalIndicators:
    """Enhanced technical indicators calculator for gold trading"""

    def __init__(self, config=None):
        # Use config values if provided, otherwise use defaults
        if config and hasattr(config, 'indicators'):
            self.macd_fast = config.indicators.macd_fast
            self.macd_slow = config.indicators.macd_slow
            self.macd_signal = config.indicators.macd_signal
            self.atr_period = config.indicators.atr_period
        else:
            # Default values
            self.macd_fast = 12
            self.macd_slow = 26
            self.macd_signal = 9
            self.atr_period = 14

        # Additional indicator parameters
        self.rsi_period = 14
        self.bb_period = 20
        self.bb_std = 2.0
        self.trend_period = 50  # For trend detection

        # Signal filtering parameters
        self.min_signal_confirmation = 0.6  # Minimum confirmation score
        self.volatility_filter = True  # Enable volatility-based filtering
        self.trend_filter = True  # Enable trend-based filtering

    def calculate_macd(self, data: pd.DataFrame,
                      fast_period: Optional[int] = None,
                      slow_period: Optional[int] = None,
                      signal_period: Optional[int] = None) -> MACDSignal:
        """
        Calculate MACD indicator

        Args:
            data: DataFrame with OHLC data
            fast_period: Fast EMA period (default: 12)
            slow_period: Slow EMA period (default: 26)
            signal_period: Signal line EMA period (default: 9)

        Returns:
            MACDSignal object with current MACD values and signals
        """
        if data is None or len(data) < 50:
            logger.warning("Insufficient data for MACD calculation")
            return MACDSignal(0, 0, 0, 'neutral', 'none')

        fast = fast_period or self.macd_fast
        slow = slow_period or self.macd_slow
        signal = signal_period or self.macd_signal

        try:
            # Calculate EMA
            ema_fast = data['close'].ewm(span=fast).mean()
            ema_slow = data['close'].ewm(span=slow).mean()

            # Calculate MACD line
            macd_line = ema_fast - ema_slow

            # Calculate signal line
            signal_line = macd_line.ewm(span=signal).mean()

            # Calculate histogram
            histogram = macd_line - signal_line

            # Get current values
            current_macd = float(macd_line.iloc[-1]) if len(macd_line) > 0 else 0
            current_signal = float(signal_line.iloc[-1]) if len(signal_line) > 0 else 0
            current_histogram = float(histogram.iloc[-1]) if len(histogram) > 0 else 0

            # Handle NaN values
            current_macd = 0 if pd.isna(current_macd) else current_macd
            current_signal = 0 if pd.isna(current_signal) else current_signal
            current_histogram = 0 if pd.isna(current_histogram) else current_histogram

            # Determine trend
            trend = self._determine_macd_trend(current_macd, current_signal, current_histogram)

            # Detect crossovers
            crossover = self._detect_macd_crossover(macd_line, signal_line)

            return MACDSignal(
                macd=current_macd,
                signal=current_signal,
                histogram=current_histogram,
                trend=trend,
                crossover=crossover
            )

        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return MACDSignal(0, 0, 0, 'neutral', 'none')

    def calculate_atr(self, data: pd.DataFrame, period: Optional[int] = None) -> ATRData:
        """
        Calculate Average True Range (ATR)

        Args:
            data: DataFrame with OHLC data
            period: ATR calculation period (default: 14)

        Returns:
            ATRData object with ATR values and volatility assessment
        """
        if data is None or len(data) < 20:
            logger.warning("Insufficient data for ATR calculation")
            return ATRData(0, 0, 'medium')

        period = period or self.atr_period

        try:
            # Calculate True Range
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)

            # Calculate ATR using EMA
            atr_values = true_range.ewm(span=period).mean()

            current_atr = float(atr_values.iloc[-1]) if len(atr_values) > 0 and not pd.isna(atr_values.iloc[-1]) else 0
            current_price = float(data['close'].iloc[-1]) if len(data) > 0 else 0

            # Calculate ATR as percentage of current price
            atr_percentage = (current_atr / current_price) * 100 if current_price > 0 else 0

            # Determine volatility level
            volatility_level = self._assess_volatility(atr_percentage)

            return ATRData(
                atr=current_atr,
                atr_percentage=atr_percentage,
                volatility_level=volatility_level
            )

        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return ATRData(0, 0, 'medium')

    def calculate_pivot_points(self, data: pd.DataFrame) -> PivotPoints:
        """
        Calculate Pivot Points (Standard method)

        Args:
            data: DataFrame with OHLC data

        Returns:
            PivotPoints object with all pivot levels
        """
        if data is None or len(data) < 2:
            logger.warning("Insufficient data for Pivot Points calculation")
            return PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot')

        try:
            # Use previous day's data for pivot calculation
            prev_high = float(data['high'].iloc[-2]) if len(data) > 1 else 0
            prev_low = float(data['low'].iloc[-2]) if len(data) > 1 else 0
            prev_close = float(data['close'].iloc[-2]) if len(data) > 1 else 0
            current_price = float(data['close'].iloc[-1]) if len(data) > 0 else 0

            # Calculate pivot point
            pivot = (prev_high + prev_low + prev_close) / 3

            # Calculate resistance levels
            r1 = 2 * pivot - prev_low
            r2 = pivot + (prev_high - prev_low)
            r3 = prev_high + 2 * (pivot - prev_low)

            # Calculate support levels
            s1 = 2 * pivot - prev_high
            s2 = pivot - (prev_high - prev_low)
            s3 = prev_low - 2 * (prev_high - pivot)

            # Determine current level relative to pivot
            current_level = self._determine_pivot_level(current_price, pivot)

            return PivotPoints(
                pivot=pivot,
                r1=r1, r2=r2, r3=r3,
                s1=s1, s2=s2, s3=s3,
                current_level=current_level
            )

        except Exception as e:
            logger.error(f"Error calculating Pivot Points: {e}")
            return PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot')

    def calculate_rsi(self, data: pd.DataFrame, period: Optional[int] = None) -> RSIData:
        """
        Calculate Relative Strength Index (RSI)

        Args:
            data: DataFrame with OHLC data
            period: RSI calculation period (default: 14)

        Returns:
            RSIData object with RSI value and condition
        """
        if data is None or len(data) < 20:
            logger.warning("Insufficient data for RSI calculation")
            return RSIData(50, 'neutral', 'none')

        period = period or self.rsi_period

        try:
            # Calculate price changes
            delta = data['close'].diff()

            # Separate gains and losses
            gains = delta.where(delta > 0, 0)
            losses = -delta.where(delta < 0, 0)

            # Calculate average gains and losses
            avg_gains = gains.rolling(window=period).mean()
            avg_losses = losses.rolling(window=period).mean()

            # Calculate RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))

            current_rsi = float(rsi.iloc[-1]) if len(rsi) > 0 and not pd.isna(rsi.iloc[-1]) else 50

            # Determine condition
            if current_rsi < 30:
                condition = 'oversold'
            elif current_rsi > 70:
                condition = 'overbought'
            else:
                condition = 'neutral'

            # Detect divergence (simplified)
            divergence = self._detect_rsi_divergence(data, rsi)

            return RSIData(
                rsi=current_rsi,
                condition=condition,
                divergence=divergence
            )

        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return RSIData(50, 'neutral', 'none')

    def calculate_bollinger_bands(self, data: pd.DataFrame,
                                period: Optional[int] = None,
                                std_dev: Optional[float] = None) -> BollingerBands:
        """
        Calculate Bollinger Bands

        Args:
            data: DataFrame with OHLC data
            period: Moving average period (default: 20)
            std_dev: Standard deviation multiplier (default: 2.0)

        Returns:
            BollingerBands object with band levels and position
        """
        if data is None or len(data) < 25:
            logger.warning("Insufficient data for Bollinger Bands calculation")
            return BollingerBands(0, 0, 0, 'between_bands', False)

        period = period or self.bb_period
        std_dev = std_dev or self.bb_std

        try:
            # Calculate moving average (middle band)
            middle = data['close'].rolling(window=period).mean()

            # Calculate standard deviation
            std = data['close'].rolling(window=period).std()

            # Calculate upper and lower bands
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)

            current_price = float(data['close'].iloc[-1])
            current_upper = float(upper.iloc[-1]) if not pd.isna(upper.iloc[-1]) else current_price
            current_middle = float(middle.iloc[-1]) if not pd.isna(middle.iloc[-1]) else current_price
            current_lower = float(lower.iloc[-1]) if not pd.isna(lower.iloc[-1]) else current_price

            # Determine position
            if current_price > current_upper:
                position = 'above_upper'
            elif current_price < current_lower:
                position = 'below_lower'
            else:
                position = 'between_bands'

            # Detect squeeze (bands contracting)
            squeeze = self._detect_bollinger_squeeze(upper, lower, period)

            return BollingerBands(
                upper=current_upper,
                middle=current_middle,
                lower=current_lower,
                position=position,
                squeeze=squeeze
            )

        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return BollingerBands(0, 0, 0, 'between_bands', False)

    def detect_market_regime(self, data: pd.DataFrame) -> MarketRegime:
        """
        Detect current market regime (trending, ranging, etc.)

        Args:
            data: DataFrame with OHLC data

        Returns:
            MarketRegime object with market condition analysis
        """
        if data is None or len(data) < 100:
            logger.warning("Insufficient data for market regime detection")
            return MarketRegime('sideways', 0.5, 'medium', 'ranging')

        try:
            # Calculate trend indicators
            sma_short = data['close'].rolling(window=20).mean()
            sma_long = data['close'].rolling(window=50).mean()

            # Trend direction
            if sma_short.iloc[-1] > sma_long.iloc[-1]:
                if sma_short.iloc[-1] > sma_short.iloc[-5]:  # Accelerating
                    trend_direction = 'bullish'
                    trend_strength = min(1.0, abs(sma_short.iloc[-1] - sma_long.iloc[-1]) / sma_long.iloc[-1] * 100)
                else:
                    trend_direction = 'sideways'
                    trend_strength = 0.3
            elif sma_short.iloc[-1] < sma_long.iloc[-1]:
                if sma_short.iloc[-1] < sma_short.iloc[-5]:  # Accelerating
                    trend_direction = 'bearish'
                    trend_strength = min(1.0, abs(sma_short.iloc[-1] - sma_long.iloc[-1]) / sma_long.iloc[-1] * 100)
                else:
                    trend_direction = 'sideways'
                    trend_strength = 0.3
            else:
                trend_direction = 'sideways'
                trend_strength = 0.1

            # Volatility regime
            atr_data = self.calculate_atr(data)
            if atr_data.atr_percentage < 0.3:
                volatility_regime = 'low'
            elif atr_data.atr_percentage < 0.6:
                volatility_regime = 'medium'
            elif atr_data.atr_percentage < 1.0:
                volatility_regime = 'high'
            else:
                volatility_regime = 'extreme'

            # Market phase
            if trend_strength > 0.7:
                market_phase = 'trending'
            elif volatility_regime == 'low' and trend_strength < 0.3:
                market_phase = 'ranging'
            elif volatility_regime in ['high', 'extreme']:
                market_phase = 'breakout'
            else:
                market_phase = 'reversal'

            return MarketRegime(
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                volatility_regime=volatility_regime,
                market_phase=market_phase
            )

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return MarketRegime('sideways', 0.5, 'medium', 'ranging')

    def get_signal_confirmation(self, data: pd.DataFrame, signal_type: str) -> SignalConfirmation:
        """
        Get comprehensive signal confirmation from multiple indicators

        Args:
            data: DataFrame with OHLC data
            signal_type: 'buy' or 'sell'

        Returns:
            SignalConfirmation object with confirmation analysis
        """
        if data is None or len(data) < 50:
            return SignalConfirmation(False, False, False, False, False, 0.0)

        try:
            # Get all indicators
            macd_signal = self.calculate_macd(data)
            rsi_data = self.calculate_rsi(data)
            bb_data = self.calculate_bollinger_bands(data)
            market_regime = self.detect_market_regime(data)

            # MACD confirmation
            if signal_type == 'buy':
                macd_confirmed = (macd_signal.crossover == 'bullish_cross' or
                                macd_signal.trend == 'bullish')
                rsi_confirmed = rsi_data.condition in ['oversold', 'neutral'] and rsi_data.rsi < 70
                bollinger_confirmed = bb_data.position in ['below_lower', 'between_bands']
                trend_confirmed = market_regime.trend_direction in ['bullish', 'sideways']
            else:  # sell
                macd_confirmed = (macd_signal.crossover == 'bearish_cross' or
                                macd_signal.trend == 'bearish')
                rsi_confirmed = rsi_data.condition in ['overbought', 'neutral'] and rsi_data.rsi > 30
                bollinger_confirmed = bb_data.position in ['above_upper', 'between_bands']
                trend_confirmed = market_regime.trend_direction in ['bearish', 'sideways']

            # Volume confirmation (simplified - using price action as proxy)
            volume_confirmed = self._check_volume_confirmation(data, signal_type)

            # Calculate overall confidence
            confirmations = [macd_confirmed, rsi_confirmed, bollinger_confirmed,
                           volume_confirmed, trend_confirmed]
            overall_confidence = sum(confirmations) / len(confirmations)

            return SignalConfirmation(
                macd_confirmed=macd_confirmed,
                rsi_confirmed=rsi_confirmed,
                bollinger_confirmed=bollinger_confirmed,
                volume_confirmed=volume_confirmed,
                trend_confirmed=trend_confirmed,
                overall_confidence=overall_confidence
            )

        except Exception as e:
            logger.error(f"Error getting signal confirmation: {e}")
            return SignalConfirmation(False, False, False, False, False, 0.0)

    def get_combined_signal(self, data: pd.DataFrame) -> Dict:
        """
        Get enhanced combined signal from all indicators with signal confirmation

        Args:
            data: DataFrame with OHLC data

        Returns:
            Dictionary with all indicator signals, confirmations, and overall assessment
        """
        # Calculate all indicators
        macd_signal = self.calculate_macd(data)
        atr_data = self.calculate_atr(data)
        pivot_points = self.calculate_pivot_points(data)
        rsi_data = self.calculate_rsi(data)
        bb_data = self.calculate_bollinger_bands(data)
        market_regime = self.detect_market_regime(data)

        # Calculate enhanced signal strength
        signal_strength = self._calculate_enhanced_signal_strength(
            macd_signal, pivot_points, rsi_data, bb_data, market_regime
        )

        # Determine preliminary recommendation
        preliminary_recommendation = self._get_trading_recommendation(
            macd_signal, atr_data, pivot_points, signal_strength
        )

        # Get signal confirmations
        buy_confirmation = self.get_signal_confirmation(data, 'buy')
        sell_confirmation = self.get_signal_confirmation(data, 'sell')

        # Final recommendation with confirmation filtering
        final_recommendation = self._get_filtered_recommendation(
            preliminary_recommendation, buy_confirmation, sell_confirmation, market_regime
        )

        return {
            'macd': {
                'value': macd_signal.macd,
                'signal': macd_signal.signal,
                'histogram': macd_signal.histogram,
                'trend': macd_signal.trend,
                'crossover': macd_signal.crossover
            },
            'rsi': {
                'value': rsi_data.rsi,
                'condition': rsi_data.condition,
                'divergence': rsi_data.divergence
            },
            'bollinger_bands': {
                'upper': bb_data.upper,
                'middle': bb_data.middle,
                'lower': bb_data.lower,
                'position': bb_data.position,
                'squeeze': bb_data.squeeze
            },
            'atr': {
                'value': atr_data.atr,
                'percentage': atr_data.atr_percentage,
                'volatility': atr_data.volatility_level
            },
            'pivot_points': {
                'pivot': pivot_points.pivot,
                'resistance': [pivot_points.r1, pivot_points.r2, pivot_points.r3],
                'support': [pivot_points.s1, pivot_points.s2, pivot_points.s3],
                'current_level': pivot_points.current_level
            },
            'market_regime': {
                'trend_direction': market_regime.trend_direction,
                'trend_strength': market_regime.trend_strength,
                'volatility_regime': market_regime.volatility_regime,
                'market_phase': market_regime.market_phase
            },
            'signal_confirmations': {
                'buy_confirmation': {
                    'macd_confirmed': buy_confirmation.macd_confirmed,
                    'rsi_confirmed': buy_confirmation.rsi_confirmed,
                    'bollinger_confirmed': buy_confirmation.bollinger_confirmed,
                    'volume_confirmed': buy_confirmation.volume_confirmed,
                    'trend_confirmed': buy_confirmation.trend_confirmed,
                    'overall_confidence': buy_confirmation.overall_confidence
                },
                'sell_confirmation': {
                    'macd_confirmed': sell_confirmation.macd_confirmed,
                    'rsi_confirmed': sell_confirmation.rsi_confirmed,
                    'bollinger_confirmed': sell_confirmation.bollinger_confirmed,
                    'volume_confirmed': sell_confirmation.volume_confirmed,
                    'trend_confirmed': sell_confirmation.trend_confirmed,
                    'overall_confidence': sell_confirmation.overall_confidence
                }
            },
            'signal_strength': signal_strength,
            'preliminary_recommendation': preliminary_recommendation,
            'final_recommendation': final_recommendation,
            'timestamp': pd.Timestamp.now()
        }

    def _determine_macd_trend(self, macd: float, signal: float, histogram: float) -> str:
        """Determine MACD trend direction"""
        if macd > signal and histogram > 0:
            return 'bullish'
        elif macd < signal and histogram < 0:
            return 'bearish'
        else:
            return 'neutral'

    def _detect_macd_crossover(self, macd_line: pd.Series, signal_line: pd.Series) -> str:
        """Detect MACD crossover signals"""
        # Check if inputs are pandas Series
        if not isinstance(macd_line, pd.Series) or not isinstance(signal_line, pd.Series):
            return 'none'

        if len(macd_line) < 2 or len(signal_line) < 2:
            return 'none'

        # Check for bullish crossover (MACD crosses above signal)
        if (macd_line.iloc[-2] <= signal_line.iloc[-2] and
            macd_line.iloc[-1] > signal_line.iloc[-1]):
            return 'bullish_cross'

        # Check for bearish crossover (MACD crosses below signal)
        elif (macd_line.iloc[-2] >= signal_line.iloc[-2] and
              macd_line.iloc[-1] < signal_line.iloc[-1]):
            return 'bearish_cross'

        return 'none'

    def _assess_volatility(self, atr_percentage: float) -> str:
        """Assess volatility level based on ATR percentage"""
        if atr_percentage < 0.5:
            return 'low'
        elif atr_percentage < 1.0:
            return 'medium'
        else:
            return 'high'

    def _determine_pivot_level(self, current_price: float, pivot: float) -> str:
        """Determine current price level relative to pivot"""
        if current_price > pivot:
            return 'above_pivot'
        elif current_price < pivot:
            return 'below_pivot'
        else:
            return 'at_pivot'

    def _calculate_signal_strength(self, macd_signal: MACDSignal,
                                 pivot_points: PivotPoints) -> float:
        """Calculate overall signal strength"""
        strength = 0.0

        # MACD contribution
        if macd_signal.crossover == 'bullish_cross':
            strength += 0.4
        elif macd_signal.crossover == 'bearish_cross':
            strength -= 0.4
        elif macd_signal.trend == 'bullish':
            strength += 0.2
        elif macd_signal.trend == 'bearish':
            strength -= 0.2

        # Pivot points contribution
        if pivot_points.current_level == 'above_pivot':
            strength += 0.2
        elif pivot_points.current_level == 'below_pivot':
            strength -= 0.2

        return abs(strength)

    def _get_trading_recommendation(self, macd_signal: MACDSignal,
                                  atr_data: ATRData,
                                  pivot_points: PivotPoints,
                                  signal_strength: float) -> str:
        """Get trading recommendation based on all indicators"""
        if signal_strength < 0.3:
            return 'hold'

        if macd_signal.trend == 'bullish' and pivot_points.current_level == 'above_pivot':
            return 'buy'
        elif macd_signal.trend == 'bearish' and pivot_points.current_level == 'below_pivot':
            return 'sell'

        return 'hold'

    def _detect_rsi_divergence(self, data: pd.DataFrame, rsi: pd.Series) -> str:
        """Detect RSI divergence (simplified implementation)"""
        try:
            if len(data) < 20 or len(rsi) < 20:
                return 'none'

            # Look for divergence in last 10 periods
            price_trend = data['close'].iloc[-10:].iloc[-1] - data['close'].iloc[-10:].iloc[0]
            rsi_trend = rsi.iloc[-10:].iloc[-1] - rsi.iloc[-10:].iloc[0]

            # Bullish divergence: price falling, RSI rising
            if price_trend < 0 and rsi_trend > 0:
                return 'bullish'
            # Bearish divergence: price rising, RSI falling
            elif price_trend > 0 and rsi_trend < 0:
                return 'bearish'

            return 'none'
        except:
            return 'none'

    def _detect_bollinger_squeeze(self, upper: pd.Series, lower: pd.Series, period: int) -> bool:
        """Detect Bollinger Band squeeze (low volatility)"""
        try:
            if len(upper) < period or len(lower) < period:
                return False

            # Calculate band width
            current_width = upper.iloc[-1] - lower.iloc[-1]
            avg_width = (upper - lower).rolling(window=period).mean().iloc[-1]

            # Squeeze if current width is significantly below average
            return current_width < (avg_width * 0.8)
        except:
            return False

    def _check_volume_confirmation(self, data: pd.DataFrame, signal_type: str) -> bool:
        """Check volume confirmation using price action as proxy"""
        try:
            if len(data) < 10:
                return False

            # Use price range as volume proxy
            recent_ranges = (data['high'] - data['low']).iloc[-5:]
            avg_range = recent_ranges.mean()
            current_range = data['high'].iloc[-1] - data['low'].iloc[-1]

            # Higher than average range suggests higher volume/interest
            return current_range > avg_range * 1.1
        except:
            return False

    def _calculate_enhanced_signal_strength(self, macd_signal: MACDSignal,
                                          pivot_points: PivotPoints,
                                          rsi_data: RSIData,
                                          bb_data: BollingerBands,
                                          market_regime: MarketRegime) -> float:
        """Calculate enhanced signal strength using multiple indicators"""
        strength = 0.0

        # MACD contribution (40%)
        if macd_signal.crossover == 'bullish_cross':
            strength += 0.4
        elif macd_signal.crossover == 'bearish_cross':
            strength -= 0.4
        elif macd_signal.trend == 'bullish':
            strength += 0.2
        elif macd_signal.trend == 'bearish':
            strength -= 0.2

        # RSI contribution (20%)
        if rsi_data.condition == 'oversold':
            strength += 0.2
        elif rsi_data.condition == 'overbought':
            strength -= 0.2
        elif rsi_data.divergence == 'bullish':
            strength += 0.15
        elif rsi_data.divergence == 'bearish':
            strength -= 0.15

        # Bollinger Bands contribution (20%)
        if bb_data.position == 'below_lower':
            strength += 0.2
        elif bb_data.position == 'above_upper':
            strength -= 0.2

        # Pivot points contribution (10%)
        if pivot_points.current_level == 'above_pivot':
            strength += 0.1
        elif pivot_points.current_level == 'below_pivot':
            strength -= 0.1

        # Market regime adjustment (10%)
        if market_regime.trend_direction == 'bullish':
            strength += 0.1 * market_regime.trend_strength
        elif market_regime.trend_direction == 'bearish':
            strength -= 0.1 * market_regime.trend_strength

        return abs(strength)

    def _get_filtered_recommendation(self, preliminary_recommendation: str,
                                   buy_confirmation: SignalConfirmation,
                                   sell_confirmation: SignalConfirmation,
                                   market_regime: MarketRegime) -> str:
        """Get filtered recommendation based on signal confirmations"""

        # Apply confirmation filters
        if preliminary_recommendation == 'buy':
            if buy_confirmation.overall_confidence >= self.min_signal_confirmation:
                # Additional market regime filter
                if market_regime.volatility_regime == 'extreme':
                    return 'hold'  # Avoid trading in extreme volatility
                return 'buy'
            else:
                return 'hold'
        elif preliminary_recommendation == 'sell':
            if sell_confirmation.overall_confidence >= self.min_signal_confirmation:
                # Additional market regime filter
                if market_regime.volatility_regime == 'extreme':
                    return 'hold'  # Avoid trading in extreme volatility
                return 'sell'
            else:
                return 'hold'

        return 'hold'

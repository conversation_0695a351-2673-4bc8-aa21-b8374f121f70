"""
Enhanced Trading Bot Class
Tích hợp tất cả các cải tiến: Risk Management, Performance Tracking, Adaptive Strategy
"""

import asyncio
import signal
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import MetaTrader5 as mt5

from .mt5_factory import create_mt5_client
from .mt5_client import PositionInfo
from ..strategy.adaptive_strategy import AdaptiveStrategy
from ..strategy.gold_strategy import MarketState
from ..risk.enhanced_risk_manager import EnhancedRiskManager
from ..indicators.technical_indicators import TechnicalIndicators
from ..performance.performance_tracker import PerformanceTracker
from ..utils.logger import setup_logger, TradingLogger
from ..utils.config import BotConfig

logger = setup_logger()
trading_logger = TradingLogger("enhanced_bot")


class EnhancedTradingBot:
    """Enhanced Trading Bot với tất cả tính năng nâng cao"""

    def __init__(self, config: BotConfig, performance_tracker: Optional[PerformanceTracker] = None):
        self.config = config
        self.running = False
        self.shutdown_requested = False

        # Initialize core components
        self.mt5_client = create_mt5_client(
            config.mt5.login,
            config.mt5.password,
            config.mt5.server,
            config.mt5.symbol,
            config.mt5.magic_number
        )

        # Initialize enhanced components
        self.indicators = TechnicalIndicators(config)
        self.risk_manager = EnhancedRiskManager(config)
        self.adaptive_strategy = AdaptiveStrategy(config)

        # Performance tracking
        self.performance_tracker = performance_tracker

        # State tracking
        self.last_analysis_time = None
        self.analysis_interval = timedelta(minutes=5)
        self.last_optimization_time = None
        self.optimization_interval = timedelta(hours=24)  # Optimize daily

        logger.info("🚀 Enhanced Trading Bot initialized")

    async def start(self):
        """Start the enhanced trading bot"""
        logger.info("🚀 Starting Enhanced MT5 Trading Bot...")
        trading_logger.log_strategy_event("ENHANCED_BOT_START", "Initializing enhanced trading bot")

        # Connect to MT5
        if not self.mt5_client.connect():
            trading_logger.log_error("MT5_CONNECTION", "Failed to connect to MT5")
            return

        trading_logger.log_connection_status("CONNECTED", "Successfully connected to MT5")

        # Validate setup
        if not self._validate_enhanced_setup():
            trading_logger.log_error("SETUP_VALIDATION", "Enhanced setup validation failed")
            return

        # Log initial account info
        account_info = self.mt5_client.get_account_info()
        if account_info:
            self._log_account_info(account_info)
        else:
            trading_logger.log_warning("ACCOUNT_INFO", "Failed to retrieve account information")

        self.running = True

        try:
            # Main enhanced trading loop
            await self._enhanced_main_loop()
        except KeyboardInterrupt:
            trading_logger.log_strategy_event("BOT_SHUTDOWN", "Shutdown requested by user")
            logger.info("👋 Shutdown requested by user")
        except Exception as e:
            trading_logger.log_error("MAIN_LOOP", f"Unexpected error in enhanced main loop: {e}")
            logger.error(f"❌ Unexpected error in enhanced main loop: {e}")
        finally:
            await self._enhanced_shutdown()

    async def _enhanced_main_loop(self):
        """Enhanced main trading loop với adaptive timing"""
        logger.info("🔄 Entering enhanced trading loop...")

        while self.running and not self.shutdown_requested:
            try:
                current_time = datetime.now()

                # Calculate next analysis time based on market conditions
                next_analysis_time = self._calculate_next_analysis_time(current_time)
                seconds_to_wait = (next_analysis_time - current_time).total_seconds()

                if seconds_to_wait > 0:
                    logger.debug(f"⏳ Waiting {seconds_to_wait:.1f} seconds until next analysis...")
                    await asyncio.sleep(seconds_to_wait)

                # Execute enhanced trading cycle
                current_time = datetime.now()
                logger.info(f"🕐 Executing enhanced trading cycle at {current_time.strftime('%H:%M:%S')}")

                await self._enhanced_trading_cycle()
                self.last_analysis_time = current_time

                # Check if we need to optimize parameters
                if self._should_optimize_parameters():
                    await self._optimize_parameters()

            except Exception as e:
                logger.error(f"❌ Error in enhanced main loop: {e}")
                await asyncio.sleep(30)

    async def _enhanced_trading_cycle(self):
        """Enhanced trading cycle với tất cả tính năng mới"""
        try:
            # Get enhanced market analysis
            market_state = await self._get_enhanced_market_state()

            if not market_state:
                logger.warning("⚠️ Could not get market state")
                return

            # Get current positions and account info
            current_positions = self.mt5_client.get_positions()
            account_info = self.mt5_client.get_account_info()

            if not account_info:
                trading_logger.log_error("ACCOUNT_INFO", "Failed to get account info")
                return

            # Log enhanced status
            self._log_enhanced_status(market_state, current_positions, account_info)

            # Generate adaptive signal
            signal = self.adaptive_strategy.generate_adaptive_signal(
                market_state, current_positions, account_info
            )

            if signal:
                # Enhanced risk check before execution
                risk_approved, risk_reason = self.risk_manager.validate_trade(
                    signal, current_positions, account_info
                )

                if risk_approved:
                    trading_logger.log_strategy_event("ENHANCED_SIGNAL_GENERATED",
                        f"{signal.action.upper()} signal with strength {signal.strength:.2f}")
                    await self._execute_enhanced_signal(signal, current_positions, market_state)
                else:
                    trading_logger.log_warning("RISK_REJECTION", f"Trade rejected by risk manager: {risk_reason}")
                    logger.info(f"🛡️ Trade rejected by risk manager: {risk_reason}")
            else:
                trading_logger.log_debug("SIGNAL_GENERATION", "No enhanced signal generated")

            # Enhanced position management
            await self._enhanced_position_management(current_positions, market_state, account_info)

            # Update performance tracking
            if self.performance_tracker:
                self.performance_tracker.update_daily_balance()

        except Exception as e:
            trading_logger.log_error("ENHANCED_TRADING_CYCLE", f"Error in enhanced trading cycle: {e}")
            logger.error(f"❌ Error in enhanced trading cycle: {e}")

    async def _get_enhanced_market_state(self) -> Optional[MarketState]:
        """Get enhanced market state với tất cả chỉ báo"""
        try:
            # Get historical data
            historical_data = self.mt5_client.get_historical_data(
                timeframe=getattr(self.config.trading, 'timeframe', 5),
                count=200
            )

            if historical_data is None or len(historical_data) < 50:
                logger.warning("⚠️ Insufficient historical data")
                return None

            # Get enhanced combined signals
            combined_signals = self.indicators.get_combined_signal(historical_data)

            # Get current price
            bid, ask = self.mt5_client.get_current_price()
            if bid is None or ask is None:
                logger.warning("⚠️ Could not get current price")
                return None

            current_price = (bid + ask) / 2
            spread = ask - bid

            # Create enhanced market state
            market_state = MarketState(
                price=current_price,
                bid=bid,
                ask=ask,
                spread=spread,
                macd_signal=combined_signals['macd_signal'],
                atr_data=combined_signals['atr_data'],
                pivot_points=combined_signals['pivot_points'],
                rsi_data=combined_signals['rsi_data'],
                bollinger_bands=combined_signals['bollinger_bands'],
                market_regime=combined_signals['market_regime'],
                signal_confirmation=combined_signals['signal_confirmations'],
                ai_prediction=None,  # Can be enhanced later
                ai_confidence=0.0,
                market_trend=combined_signals['market_regime'].trend_direction,
                volatility=combined_signals['atr_data'].volatility_level
            )

            return market_state

        except Exception as e:
            logger.error(f"❌ Error getting enhanced market state: {e}")
            return None

    async def _execute_enhanced_signal(self, signal, current_positions, market_state):
        """Execute signal với enhanced risk management"""
        try:
            # Calculate enhanced position size
            enhanced_volume = self.risk_manager.calculate_position_size(
                symbol=self.config.mt5.symbol,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                account_balance=self.mt5_client.get_account_info()['balance']
            )

            # Use enhanced volume if different from signal
            if abs(enhanced_volume - signal.volume) > 0.001:
                logger.info(f"🎯 Risk manager adjusted volume: {signal.volume:.3f} → {enhanced_volume:.3f}")
                signal.volume = enhanced_volume

            # Record trade entry for performance tracking
            if self.performance_tracker and signal.action in ['buy', 'sell']:
                self.performance_tracker.record_trade_entry(
                    trade_id=f"T{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    symbol=self.config.mt5.symbol,
                    direction='long' if signal.action == 'buy' else 'short',
                    entry_price=signal.entry_price,
                    volume=signal.volume,
                    stop_loss=signal.stop_loss,
                    take_profit=signal.take_profit,
                    market_condition=market_state.market_regime.market_phase,
                    signal_confidence=signal.strength
                )

            # Execute the trade (similar to original but with enhanced logging)
            if not self.config.live_trading:
                logger.info(f"🧪 DEMO MODE - Would execute: {signal.action.upper()} "
                          f"{signal.volume:.3f} @ {signal.entry_price:.5f} "
                          f"(Strength: {signal.strength:.2f}, Market: {market_state.market_regime.market_phase})")
                return

            # Execute real trade
            result = None
            if signal.action == 'buy':
                result = self.mt5_client.buy_market(
                    volume=signal.volume,
                    sl=signal.stop_loss,
                    tp=signal.take_profit
                )
            elif signal.action == 'sell':
                result = self.mt5_client.sell_market(
                    volume=signal.volume,
                    sl=signal.stop_loss,
                    tp=signal.take_profit
                )

            if result:
                logger.info(f"✅ {signal.action.upper()} order executed: {result}")
                trading_logger.log_trade_entry(
                    self.config.mt5.symbol,
                    signal.action.upper(),
                    signal.volume,
                    signal.entry_price,
                    signal.stop_loss,
                    signal.take_profit,
                    strength=signal.strength,
                    reasoning=f"Enhanced signal - {market_state.market_regime.market_phase}"
                )
            else:
                logger.error(f"❌ Failed to execute {signal.action.upper()} order")

        except Exception as e:
            logger.error(f"❌ Error executing enhanced signal: {e}")

    async def _enhanced_position_management(self, positions, market_state, account_info):
        """Enhanced position management với dynamic stops"""
        for position in positions:
            try:
                # Enhanced risk management check
                should_close, reason = self.risk_manager.should_close_position(
                    position, market_state, account_info
                )

                if should_close and self.config.live_trading:
                    result = self.mt5_client.close_position(position.ticket)
                    if result:
                        # Record trade exit for performance tracking
                        if self.performance_tracker:
                            self.performance_tracker.record_trade_exit(
                                trade_id=str(position.ticket),
                                exit_price=position.price_current,
                                commission=0.0,  # MT5 will provide this
                                swap=position.swap,
                                exit_reason=reason
                            )

                        logger.info(f"🛡️ Enhanced risk management closed position {position.ticket}: {reason}")
                        trading_logger.log_trade_exit(
                            position.symbol,
                            'ENHANCED_RISK_CLOSE',
                            position.volume,
                            position.price_open,
                            position.price_current,
                            position.profit,
                            reasoning=reason
                        )

                # Update trailing stops if enabled
                elif hasattr(self.config.risk, 'trailing_stop_enabled') and self.config.risk.trailing_stop_enabled:
                    await self._update_trailing_stop(position, market_state)

            except Exception as e:
                logger.error(f"❌ Error in enhanced position management for {position.ticket}: {e}")

    async def _update_trailing_stop(self, position, market_state):
        """Update trailing stop loss"""
        try:
            new_sl = self.risk_manager.calculate_trailing_stop(position, market_state)

            if new_sl and abs(new_sl - position.sl) > 0.0001:  # Only update if significant change
                result = self.mt5_client.modify_position(position.ticket, sl=new_sl)
                if result:
                    logger.info(f"📈 Updated trailing stop for {position.ticket}: {position.sl:.5f} → {new_sl:.5f}")

        except Exception as e:
            logger.error(f"❌ Error updating trailing stop for {position.ticket}: {e}")

    def _calculate_next_analysis_time(self, current_time):
        """Calculate next analysis time based on market conditions"""
        # Default to 5-minute intervals
        base_interval = 5

        # Adjust based on market volatility if we have market state
        # For now, use fixed 5-minute intervals
        next_time = (current_time.replace(second=0, microsecond=0)
                    + timedelta(minutes=base_interval - current_time.minute % base_interval))

        return next_time

    def _should_optimize_parameters(self):
        """Check if we should run parameter optimization"""
        if not self.last_optimization_time:
            return False

        return datetime.now() - self.last_optimization_time > self.optimization_interval

    async def _optimize_parameters(self):
        """Run parameter optimization"""
        try:
            logger.info("🔧 Running parameter optimization...")
            # This would integrate with the optimization framework
            # For now, just update the timestamp
            self.last_optimization_time = datetime.now()

        except Exception as e:
            logger.error(f"❌ Error in parameter optimization: {e}")

    def _validate_enhanced_setup(self) -> bool:
        """Validate enhanced bot setup"""
        # Basic validation from original bot
        account_info = self.mt5_client.get_account_info()
        if not account_info:
            logger.error("❌ Cannot get account information")
            return False

        symbol_info = self.mt5_client.get_symbol_info()
        if not symbol_info:
            logger.error(f"❌ Symbol {self.config.mt5.symbol} not available")
            return False

        bid, ask = self.mt5_client.get_current_price()
        if bid is None or ask is None:
            logger.error("❌ Cannot get current prices - market may be closed")
            return False

        # Enhanced validation
        try:
            # Test indicators
            test_data = self.mt5_client.get_historical_data(
                timeframe=5,
                count=50
            )

            if test_data is None or len(test_data) < 20:
                logger.error("❌ Insufficient historical data for indicators")
                return False

            # Test enhanced components
            self.indicators.get_combined_signal(test_data)
            logger.info("✅ Enhanced indicators validated")

        except Exception as e:
            logger.error(f"❌ Enhanced setup validation failed: {e}")
            return False

        if not self.config.live_trading:
            logger.warning("🧪 Running in DEMO mode - no real trades will be executed")

        logger.info("✅ Enhanced setup validation passed")
        return True

    def _log_account_info(self, account_info):
        """Log enhanced account information"""
        trading_logger.log_balance_update(
            0, account_info['balance'], account_info['balance'],
            "Initial enhanced account balance"
        )

        logger.info(f"💰 Account Balance: ${account_info['balance']:,.2f}")
        logger.info(f"📊 Account Equity: ${account_info['equity']:,.2f}")
        logger.info(f"⚡ Leverage: 1:{account_info['leverage']}")
        logger.info(f"🛡️ Margin Level: {account_info.get('margin_level', 0):.2f}%")

        trading_logger.log_market_data("ENHANCED_ACCOUNT_INFO", {
            'balance': account_info['balance'],
            'equity': account_info['equity'],
            'leverage': account_info['leverage'],
            'margin_level': account_info.get('margin_level', 0),
            'enhanced_features': True
        })

    def _log_enhanced_status(self, market_state, positions, account_info):
        """Log enhanced bot status"""
        # Log every 10 minutes to avoid spam
        if (self.last_analysis_time and
            datetime.now() - self.last_analysis_time < timedelta(minutes=10)):
            return

        logger.info(f"=== 🚀 Enhanced Trading Bot Status ===")
        logger.info(f"💰 Price: {market_state.price:.5f} | Spread: {market_state.spread:.1f}")

        # Enhanced market information
        logger.info(f"📊 Market Regime: {market_state.market_regime.market_phase}")
        logger.info(f"📈 Trend: {market_state.market_regime.trend_direction} "
                   f"(Strength: {market_state.market_regime.trend_strength:.2f})")
        logger.info(f"⚡ Volatility: {market_state.market_regime.volatility_regime}")

        # Technical indicators
        logger.info(f"🎯 MACD: {market_state.macd_signal.trend} | "
                   f"RSI: {market_state.rsi_data.condition} | "
                   f"BB: {market_state.bollinger_bands.position}")

        # Signal confirmation
        total_score = market_state.signal_confirmation.get('total_score', 0)
        logger.info(f"🎲 Signal Score: {total_score}/100")

        # Account and positions
        logger.info(f"💰 Balance: ${account_info['balance']:,.2f} | "
                   f"Equity: ${account_info['equity']:,.2f} | "
                   f"Positions: {len(positions)}")

        if positions:
            total_profit = sum(pos.profit for pos in positions)
            logger.info(f"📊 Open P&L: ${total_profit:.2f}")

        # Performance summary if available
        if self.performance_tracker and self.performance_tracker.trades:
            metrics = self.performance_tracker.get_current_metrics()
            logger.info(f"📈 Performance: {metrics.total_trades} trades, "
                       f"{metrics.win_rate:.1%} win rate, "
                       f"${metrics.total_pnl:.2f} total P&L")

    async def _enhanced_shutdown(self):
        """Enhanced shutdown with performance reporting"""
        logger.info("🛑 Shutting down enhanced trading bot...")

        self.running = False

        # Close all positions if configured
        if self.config.live_trading and hasattr(self.config, 'close_on_shutdown'):
            if self.config.close_on_shutdown:
                positions = self.mt5_client.get_positions()
                if positions:
                    logger.info(f"🔄 Closing {len(positions)} open positions...")
                    results = self.mt5_client.close_all_positions()
                    logger.info(f"✅ Closed {len(results)} positions")

        # Generate final performance report
        if self.performance_tracker:
            try:
                from ..performance.dashboard import PerformanceDashboard
                dashboard = PerformanceDashboard(self.performance_tracker)

                # Export final report
                report_path = dashboard.export_dashboard_data("final_report.json")
                logger.info(f"📄 Final performance report saved: {report_path}")

                # Print summary
                metrics = self.performance_tracker.get_current_metrics()
                logger.info(f"📊 Final Performance Summary:")
                logger.info(f"   Total Trades: {metrics.total_trades}")
                logger.info(f"   Win Rate: {metrics.win_rate:.1%}")
                logger.info(f"   Total P&L: ${metrics.total_pnl:.2f}")
                logger.info(f"   Max Drawdown: {metrics.max_drawdown_pct:.2%}")
                logger.info(f"   Sharpe Ratio: {metrics.sharpe_ratio:.2f}")

            except Exception as e:
                logger.warning(f"⚠️ Could not generate final performance report: {e}")

        # Disconnect from MT5
        self.mt5_client.disconnect()

        logger.info("✅ Enhanced trading bot shutdown complete")

    def request_shutdown(self):
        """Request graceful shutdown"""
        self.shutdown_requested = True
        logger.info("🛑 Enhanced bot shutdown requested")

    def get_enhanced_status(self) -> Dict:
        """Get enhanced bot status"""
        base_status = {
            'running': self.running,
            'enhanced_features': True,
            'live_trading': self.config.live_trading,
            'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
            'performance_tracking': self.performance_tracker is not None
        }

        # Add performance metrics if available
        if self.performance_tracker:
            try:
                metrics = self.performance_tracker.get_current_metrics()
                base_status.update({
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_pnl': metrics.total_pnl,
                    'current_drawdown_pct': metrics.current_drawdown_pct,
                    'sharpe_ratio': metrics.sharpe_ratio
                })
            except:
                pass

        return base_status

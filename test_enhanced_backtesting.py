#!/usr/bin/env python3
"""
Test Enhanced Backtesting System
Quick test to verify the enhanced backtesting features
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.backtesting.backtest_engine import BacktestEngine, SimpleBacktestConfig
from src.utils.config import load_config
from src.utils.logger import get_logger

logger = get_logger(__name__)


def create_test_data(periods=1000):
    """Create realistic test data for backtesting"""
    np.random.seed(42)
    
    # Generate realistic gold price data
    base_price = 2000
    dates = pd.date_range(start='2024-01-01', periods=periods, freq='5T')
    
    # Generate price movements with trend and volatility
    returns = np.random.normal(0.0001, 0.002, periods)  # Small upward bias
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # Create OHLC data
    data = []
    for i, price in enumerate(prices):
        volatility = abs(np.random.normal(0, 0.001))
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        
        data.append({
            'open': open_price,
            'high': max(high, open_price, close_price),
            'low': min(low, open_price, close_price),
            'close': close_price,
            'volume': np.random.randint(100, 1000)
        })
    
    df = pd.DataFrame(data, index=dates)
    return df


def test_simple_backtest_config():
    """Test SimpleBacktestConfig functionality"""
    print("🧪 Testing SimpleBacktestConfig")
    print("-" * 40)
    
    # Test quick setup
    config_1month = SimpleBacktestConfig.quick_setup("1month", 5000.0)
    print(f"✅ 1 Month Config: {config_1month.start_date} to {config_1month.end_date}")
    print(f"   Balance: ${config_1month.initial_balance}")
    
    config_3months = SimpleBacktestConfig.quick_setup("3months", 10000.0)
    print(f"✅ 3 Months Config: {config_3months.start_date} to {config_3months.end_date}")
    
    # Test custom config
    custom_config = SimpleBacktestConfig(
        start_date="2024-01-01",
        end_date="2024-06-30",
        initial_balance=15000.0,
        timeframes=['M5', 'M15', 'H1']
    )
    print(f"✅ Custom Config: {custom_config.start_date} to {custom_config.end_date}")
    print(f"   Timeframes: {custom_config.timeframes}")
    
    return True


def test_enhanced_metrics():
    """Test enhanced backtesting metrics"""
    print("\n🧪 Testing Enhanced Backtesting Metrics")
    print("-" * 50)
    
    try:
        # Load config
        config = load_config()
        
        # Create test data
        test_data = create_test_data(500)  # 500 bars of 5-minute data
        print(f"✅ Created test data: {len(test_data)} bars")
        
        # Create backtest engine
        engine = BacktestEngine(config)
        
        # Run backtest with test data
        start_date = test_data.index[0]
        end_date = test_data.index[-1]
        
        print(f"📅 Running backtest from {start_date} to {end_date}")
        
        results = engine.run_backtest(test_data, start_date, end_date)
        
        # Test basic metrics
        print(f"✅ Basic Metrics:")
        print(f"   Total Trades: {results.total_trades}")
        print(f"   Win Rate: {results.win_rate:.2%}")
        print(f"   Total Return: {results.total_return_pct:.2%}")
        print(f"   Max Drawdown: {results.max_drawdown_pct:.2%}")
        
        # Test enhanced metrics
        print(f"✅ Enhanced Metrics:")
        print(f"   VaR (95%): {results.var_95:.4f}")
        print(f"   CVaR (95%): {results.cvar_95:.4f}")
        print(f"   Kelly Criterion: {results.kelly_criterion:.2%}")
        print(f"   Expectancy: ${results.expectancy:.2f}")
        print(f"   Recovery Factor: {results.recovery_factor:.2f}")
        print(f"   Ulcer Index: {results.ulcer_index:.4f}")
        
        # Test risk-adjusted metrics
        print(f"✅ Risk-Adjusted Metrics:")
        print(f"   Sharpe Ratio: {results.sharpe_ratio:.2f}")
        print(f"   Sortino Ratio: {results.sortino_ratio:.2f}")
        print(f"   Information Ratio: {results.information_ratio:.2f}")
        print(f"   Treynor Ratio: {results.treynor_ratio:.2f}")
        
        # Test performance by condition
        if results.performance_by_condition:
            print(f"✅ Performance by Market Condition:")
            for condition, perf in results.performance_by_condition.items():
                print(f"   {condition}: {perf['trades']} trades, "
                      f"Win Rate: {perf['win_rate']:.1%}")
        
        # Test monthly returns
        if len(results.monthly_returns) > 0:
            print(f"✅ Monthly Returns: {len(results.monthly_returns)} months analyzed")
        
        # Test trade timing stats
        if results.trade_timing_stats:
            print(f"✅ Trade Timing Stats:")
            print(f"   Best Hour: {results.trade_timing_stats.get('best_hour', 'N/A')}")
            print(f"   Best Day: {results.trade_timing_stats.get('best_day', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced metrics: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simple_backtest_interface():
    """Test the simplified backtest interface"""
    print("\n🧪 Testing Simple Backtest Interface")
    print("-" * 45)
    
    try:
        # Load config
        config = load_config()
        
        # Create backtest engine
        engine = BacktestEngine(config)
        
        # Test simple configuration
        simple_config = SimpleBacktestConfig.quick_setup("1month", 5000.0)
        
        print(f"📅 Testing period: {simple_config.start_date} to {simple_config.end_date}")
        print(f"💰 Initial balance: ${simple_config.initial_balance}")
        
        # Note: This would normally call the data manager, but for testing
        # we'll simulate the process
        print("✅ Simple backtest interface configured successfully")
        print("   (Actual data retrieval would happen in real execution)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing simple interface: {e}")
        return False


def test_performance_rating():
    """Test performance rating system"""
    print("\n🧪 Testing Performance Rating System")
    print("-" * 42)
    
    # Create mock results for testing
    class MockResults:
        def __init__(self, win_rate, profit_factor, sharpe_ratio, max_drawdown_pct):
            self.win_rate = win_rate
            self.profit_factor = profit_factor
            self.sharpe_ratio = sharpe_ratio
            self.max_drawdown_pct = max_drawdown_pct
    
    # Test different performance scenarios
    scenarios = [
        ("Excellent Strategy", MockResults(0.65, 2.5, 2.2, 0.04)),
        ("Good Strategy", MockResults(0.55, 1.8, 1.5, 0.08)),
        ("Average Strategy", MockResults(0.45, 1.2, 0.8, 0.12)),
        ("Poor Strategy", MockResults(0.35, 0.9, 0.3, 0.25))
    ]
    
    # Import the rating function
    sys.path.append(str(Path(__file__).parent))
    from enhanced_backtest import get_performance_rating
    
    for name, results in scenarios:
        rating = get_performance_rating(results)
        print(f"✅ {name}: {rating}")
    
    return True


def main():
    """Run all enhanced backtesting tests"""
    print("🚀 Testing Enhanced Backtesting System")
    print("=" * 60)
    
    tests = [
        ("SimpleBacktestConfig", test_simple_backtest_config),
        ("Enhanced Metrics", test_enhanced_metrics),
        ("Simple Interface", test_simple_backtest_interface),
        ("Performance Rating", test_performance_rating)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All enhanced backtesting features working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

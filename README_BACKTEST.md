# 🚀 Quick Backtest - Hướng Dẫn Nhanh

## Cách sử dụng đơn giản nhất:

### 1. <PERSON>em các preset có sẵn:
```bash
python quick_backtest.py --list-presets
```

### 2. Chạy backtest với preset:
```bash
# Backtest tháng trước
python quick_backtest.py --preset last_month

# Backtest tuần trước  
python quick_backtest.py --preset last_week

# Backtest quý 2/2024
python quick_backtest.py --preset q2_2024
```

### 3. Chạy backtest với ngày cụ thể:
```bash
python quick_backtest.py --start 2024-06-01 --end 2024-07-01
```

### 4. Thay đổi số dư ban đầu:
```bash
python quick_backtest.py --preset last_month --balance 1000
```

## C<PERSON><PERSON> preset có sẵn:
- `last_week` - <PERSON><PERSON><PERSON> trước
- `last_month` - <PERSON><PERSON><PERSON>g trước  
- `last_3months` - 3 tháng trước
- `last_6months` - 6 tháng trước
- `last_year` - <PERSON><PERSON>m tr<PERSON>ớc
- `ytd` - Từ đầu năm
- `q1_2024`, `q2_2024`, `q3_2024`, `q4_2024` - Các quý 2024

## Kết quả:
- Hiển thị trên console
- File HTML report trong thư mục `reports/`
- Các biểu đồ phân tích trong thư mục `reports/`

## Cách cũ (vẫn hoạt động):
1. Chỉnh sửa `config/config.yaml`:
```yaml
backtest:
  start_date: '2024-06-01'
  end_date: '2024-07-01'
```

2. Chạy:
```bash
python run_backtest.py
```

---

**📖 Xem hướng dẫn chi tiết trong file `BACKTEST_GUIDE.md`**

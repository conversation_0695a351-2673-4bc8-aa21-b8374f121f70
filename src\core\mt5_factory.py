"""
MT5 Client Factory
Automatically selects the appropriate MT5 client based on platform and availability
"""

import platform
import sys
from typing import Union

from ..utils.logger import get_logger

logger = get_logger(__name__)


def create_mt5_client(login: int, password: str, server: str, symbol: str, magic_number: int):
    """
    Factory function to create appropriate MT5 client

    Args:
        login: MT5 account login
        password: MT5 account password
        server: MT5 server name
        symbol: Trading symbol
        magic_number: Magic number for orders

    Returns:
        MT5Client or MT5ClientMock instance
    """
    system = platform.system()

    if system == "Windows":
        # Try to use real MT5 client on Windows
        try:
            from .mt5_client import MT5Client
            logger.info("Using real MT5 client on Windows")
            return MT5Client(login, password, server, symbol, magic_number)
        except ImportError as e:
            logger.warning(f"MT5 package not available: {e}")
            logger.info("Falling back to mock client")
            from .mt5_client_mock import MT5ClientMock
            return MT5ClientMock(login, password, server, symbol, magic_number)

    else:
        # Use mock client on macOS/Linux
        logger.info(f"Using mock MT5 client on {system}")
        logger.warning("🚨 MOCK MODE: No real trading will occur!")
        from .mt5_client_mock import MT5ClientMock
        return MT5ClientMock(login, password, server, symbol, magic_number)


def get_mt5_constants():
    """
    Get MT5 constants, with fallbacks for mock client

    Returns:
        Dictionary with MT5 constants
    """
    try:
        import MetaTrader5 as mt5
        return {
            'TIMEFRAME_M5': mt5.TIMEFRAME_M5,
            'TIMEFRAME_M15': mt5.TIMEFRAME_M15,
            'TIMEFRAME_H1': mt5.TIMEFRAME_H1,
            'TIMEFRAME_D1': mt5.TIMEFRAME_D1,
            'ORDER_TYPE_BUY': mt5.ORDER_TYPE_BUY,
            'ORDER_TYPE_SELL': mt5.ORDER_TYPE_SELL,
            'TRADE_ACTION_DEAL': mt5.TRADE_ACTION_DEAL,
            'ORDER_TIME_GTC': mt5.ORDER_TIME_GTC,
            'ORDER_FILLING_IOC': mt5.ORDER_FILLING_IOC,
        }
    except ImportError:
        # Mock constants
        return {
            'TIMEFRAME_M5': 5,
            'TIMEFRAME_M15': 15,
            'TIMEFRAME_H1': 60,
            'TIMEFRAME_D1': 1440,
            'ORDER_TYPE_BUY': 0,
            'ORDER_TYPE_SELL': 1,
            'TRADE_ACTION_DEAL': 1,
            'ORDER_TIME_GTC': 0,
            'ORDER_FILLING_IOC': 1,
        }


# Global constants
MT5_CONSTANTS = get_mt5_constants()

# Export commonly used constants
TIMEFRAME_M5 = MT5_CONSTANTS['TIMEFRAME_M5']
TIMEFRAME_M15 = MT5_CONSTANTS['TIMEFRAME_M15']
TIMEFRAME_H1 = MT5_CONSTANTS['TIMEFRAME_H1']
TIMEFRAME_D1 = MT5_CONSTANTS['TIMEFRAME_D1']

ORDER_TYPE_BUY = MT5_CONSTANTS['ORDER_TYPE_BUY']
ORDER_TYPE_SELL = MT5_CONSTANTS['ORDER_TYPE_SELL']
TRADE_ACTION_DEAL = MT5_CONSTANTS['TRADE_ACTION_DEAL']
ORDER_TIME_GTC = MT5_CONSTANTS['ORDER_TIME_GTC']
ORDER_FILLING_IOC = MT5_CONSTANTS['ORDER_FILLING_IOC']

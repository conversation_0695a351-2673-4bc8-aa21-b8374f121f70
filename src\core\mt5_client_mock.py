"""
MT5 Client Mock for macOS/Linux Development
Provides a mock implementation for testing and development on non-Windows systems
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
from dataclasses import dataclass
import logging
import yfinance as yf
import time
import random

from ..utils.logger import get_logger
from .mt5_client import TradeRequest, PositionInfo

logger = get_logger(__name__)


class MT5ClientMock:
    """Mock MT5 Client for macOS/Linux development and testing"""

    def __init__(self, login: int, password: str, server: str, symbol: str, magic_number: int,
                 mock_balance: float = 10000.0, mock_equity: float = 10000.0,
                 base_price: float = 2000.0, price_volatility: float = 0.001):
        self.login = login
        self.password = password
        self.server = server
        self.connected = False
        self.symbol = symbol
        self.magic_number = magic_number

        # Mock data from config
        self.mock_balance = mock_balance
        self.mock_equity = mock_equity
        self.mock_positions = []
        self.mock_orders = []
        self.order_counter = 1000

        # Price simulation from config
        self.base_price = base_price
        self.current_price = self.base_price
        self.price_volatility = price_volatility

        logger.info("MT5 Mock Client initialized (for macOS/Linux development)")

    def connect(self) -> bool:
        """Mock connection to MT5"""
        try:
            # Simulate connection delay
            time.sleep(1)

            # Mock validation
            if self.login <= 0:
                logger.error("Invalid login credentials")
                return False

            self.connected = True
            logger.info(f"Mock MT5 connected - Account: {self.login}, Server: {self.server}")
            logger.warning("🚨 USING MOCK MT5 CLIENT - NO REAL TRADING!")

            return True

        except Exception as e:
            logger.error(f"Mock connection error: {e}")
            return False

    def disconnect(self):
        """Mock disconnect from MT5"""
        if self.connected:
            self.connected = False
            logger.info("Disconnected from Mock MT5")

    def get_account_info(self) -> Optional[Dict]:
        """Get mock account information"""
        if not self.connected:
            logger.error("Not connected to Mock MT5")
            return None

        # Update equity based on positions
        total_profit = sum(pos.profit for pos in self.mock_positions)
        self.mock_equity = self.mock_balance + total_profit

        return {
            'login': self.login,
            'balance': self.mock_balance,
            'equity': self.mock_equity,
            'margin': 0.0,
            'free_margin': self.mock_equity,
            'margin_level': 999999.0,  # Very high margin level
            'currency': 'USD',
            'server': self.server,
            'leverage': 100
        }

    def get_symbol_info(self) -> Optional[Dict]:
        """Get mock symbol information"""
        if not self.connected:
            logger.error("Not connected to Mock MT5")
            return None

        bid, ask = self.get_current_price()
        spread = ask - bid

        return {
            'symbol': self.symbol,
            'bid': bid,
            'ask': ask,
            'spread': spread * 100000,  # Convert to points
            'digits': 5,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 100.0,
            'volume_step': 0.01,
            'contract_size': 100
        }

    def get_current_price(self) -> Optional[Tuple[float, float]]:
        """Get mock current bid and ask prices"""
        if not self.connected:
            logger.error("Not connected to Mock MT5")
            return None

        # Simulate price movement
        price_change = np.random.normal(0, self.price_volatility)
        self.current_price *= (1 + price_change)

        # Keep price in reasonable range
        self.current_price = max(1800, min(2200, self.current_price))

        # Simulate spread
        spread = 0.0002  # 2 pips
        bid = self.current_price - spread/2
        ask = self.current_price + spread/2

        return bid, ask

    def get_historical_data(self, timeframe: int, count: int = 1000) -> Optional[pd.DataFrame]:
        """Get mock historical price data"""
        if not self.connected:
            logger.error("Not connected to Mock MT5")
            return None

        try:
            # Try to get real gold data from Yahoo Finance
            ticker = yf.Ticker("GC=F")  # Gold futures

            # Calculate period based on timeframe and count
            if timeframe == 5:  # 5 minutes
                period = "5d"
                interval = "5m"
            elif timeframe == 15:  # 15 minutes
                period = "1mo"
                interval = "15m"
            elif timeframe == 60:  # 1 hour
                period = "3mo"
                interval = "1h"
            else:
                period = "1y"
                interval = "1d"

            data = ticker.history(period=period, interval=interval)

            if data.empty:
                # Fallback to simulated data
                return self._generate_mock_data(count)

            # Convert to MT5 format
            data = data.tail(count)  # Get last 'count' bars
            data.reset_index(inplace=True)

            # Rename columns to match MT5 format
            data.rename(columns={
                'Datetime': 'time',
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            }, inplace=True)

            # Add missing columns
            data['tick_volume'] = data['volume']
            data['spread'] = 20  # Mock spread

            # Set time as index
            data.set_index('time', inplace=True)

            logger.info(f"Retrieved {len(data)} bars of real gold data")
            return data

        except Exception as e:
            logger.warning(f"Failed to get real data, using mock: {e}")
            return self._generate_mock_data(count)

    def _generate_mock_data(self, count: int) -> pd.DataFrame:
        """Generate mock historical data"""
        # Generate timestamps
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=5 * count)
        timestamps = pd.date_range(start_time, end_time, periods=count)

        # Generate realistic price data using random walk
        np.random.seed(42)  # For reproducible data
        returns = np.random.normal(0, 0.002, count)  # 0.2% volatility

        prices = [self.base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)

        # Create OHLC data
        data = []
        for i, (timestamp, close_price) in enumerate(zip(timestamps, prices)):
            open_price = prices[i-1] if i > 0 else close_price

            # Generate high and low
            volatility = abs(np.random.normal(0, 0.001))
            high = max(open_price, close_price) * (1 + volatility)
            low = min(open_price, close_price) * (1 - volatility)

            data.append({
                'time': timestamp,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': np.random.randint(100, 1000),
                'tick_volume': np.random.randint(50, 500),
                'spread': 20
            })

        df = pd.DataFrame(data)
        df.set_index('time', inplace=True)

        logger.info(f"Generated {len(df)} bars of mock data")
        return df

    def send_order(self, trade_request: TradeRequest) -> Optional[Dict]:
        """Mock send trading order"""
        if not self.connected:
            logger.error("Not connected to Mock MT5")
            return None

        # Simulate order execution
        order_id = self.order_counter
        self.order_counter += 1

        # Mock successful execution
        result = {
            'retcode': 10009,  # TRADE_RETCODE_DONE
            'deal': order_id,
            'order': order_id,
            'volume': trade_request.volume,
            'price': trade_request.price,
            'comment': f"Mock order {order_id}"
        }

        # Create mock position
        position = PositionInfo(
            ticket=order_id,
            symbol=trade_request.symbol,
            type=trade_request.type,
            volume=trade_request.volume,
            price_open=trade_request.price,
            price_current=trade_request.price,
            profit=0.0,
            swap=0.0,
            commission=0.0,
            time=datetime.now()
        )

        self.mock_positions.append(position)

        logger.info(f"Mock order executed: {trade_request.action} {trade_request.volume} @ {trade_request.price}")
        logger.warning("🚨 MOCK TRADE - NO REAL MONEY INVOLVED!")

        return result

    def buy_market(self, volume: float, sl: float = 0.0, tp: float = 0.0) -> Optional[Dict]:
        """Mock place market buy order"""
        bid, ask = self.get_current_price()
        if bid is None or ask is None:
            return None

        trade_request = TradeRequest(
            action=1,  # TRADE_ACTION_DEAL
            symbol=self.symbol,
            volume=volume,
            type=0,  # ORDER_TYPE_BUY
            deviation=20,  # Default deviation
            magic=self.magic_number,
            comment="Mock buy order",
            price=ask,
            sl=sl,
            tp=tp
        )

        return self.send_order(trade_request)

    def sell_market(self, volume: float, sl: float = 0.0, tp: float = 0.0) -> Optional[Dict]:
        """Mock place market sell order"""
        bid, ask = self.get_current_price()
        if bid is None or ask is None:
            return None

        trade_request = TradeRequest(
            action=1,  # TRADE_ACTION_DEAL
            symbol=self.symbol,
            volume=volume,
            type=1,  # ORDER_TYPE_SELL
            deviation=20,  # Default deviation
            magic=self.magic_number,
            comment="Mock sell order",
            price=bid,
            sl=sl,
            tp=tp
        )

        return self.send_order(trade_request)

    def get_positions(self) -> List[PositionInfo]:
        """Get mock current positions"""
        if not self.connected:
            logger.error("Not connected to Mock MT5")
            return []

        # Update position profits based on current price
        current_bid, current_ask = self.get_current_price()
        current_price = (current_bid + current_ask) / 2

        for position in self.mock_positions:
            if position.type == 0:  # Buy position
                position.price_current = current_bid
                position.profit = (current_bid - position.price_open) * position.volume * 100
            else:  # Sell position
                position.price_current = current_ask
                position.profit = (position.price_open - current_ask) * position.volume * 100

        return self.mock_positions.copy()

    def close_position(self, ticket: int) -> Optional[Dict]:
        """Mock close position by ticket"""
        position_to_close = None
        for i, position in enumerate(self.mock_positions):
            if position.ticket == ticket:
                position_to_close = position
                del self.mock_positions[i]
                break

        if position_to_close is None:
            logger.error(f"Mock position {ticket} not found")
            return None

        # Update balance with profit/loss
        self.mock_balance += position_to_close.profit

        result = {
            'retcode': 10009,
            'deal': ticket,
            'order': ticket,
            'volume': position_to_close.volume,
            'price': position_to_close.price_current,
            'comment': f"Mock close {ticket}"
        }

        logger.info(f"Mock position {ticket} closed: P&L ${position_to_close.profit:.2f}")
        logger.warning("🚨 MOCK TRADE CLOSE - NO REAL MONEY INVOLVED!")

        return result

    def close_all_positions(self) -> List[Dict]:
        """Mock close all positions"""
        results = []
        positions_to_close = self.mock_positions.copy()

        for position in positions_to_close:
            result = self.close_position(position.ticket)
            if result:
                results.append(result)

        return results

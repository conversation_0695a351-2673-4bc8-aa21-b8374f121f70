"""
Enhanced Risk Manager
<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>ủi ro nâng cao v<PERSON><PERSON>, ATR dynamic stops, và portfolio controls
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from .risk_manager import RiskManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class EnhancedRiskManager(RiskManager):
    """Enhanced Risk Manager với tính năng nâng cao"""
    
    def __init__(self, config):
        super().__init__(config)
        self.config = config
        
        # Enhanced risk parameters
        self.kelly_fraction = getattr(config.risk, 'kelly_fraction', 0.25)
        self.max_portfolio_risk = getattr(config.risk, 'max_portfolio_risk', 0.06)
        self.trailing_stop_enabled = getattr(config.risk, 'trailing_stop_enabled', True)
        self.stop_loss_atr_multiplier = getattr(config.risk, 'stop_loss_atr_multiplier', 2.0)
        self.take_profit_atr_multiplier = getattr(config.risk, 'take_profit_atr_multiplier', 3.0)
        
        # Risk tracking
        self.consecutive_losses = 0
        self.max_consecutive_losses = getattr(config.risk, 'max_consecutive_losses', 5)
        self.daily_loss_limit = getattr(config.risk, 'daily_loss_limit', 0.05)  # 5% daily loss limit
        
        logger.info("Enhanced Risk Manager initialized")
    
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss: float, account_balance: float) -> float:
        """Calculate optimal position size using Kelly Criterion and risk management"""
        try:
            # Basic risk per trade
            risk_per_trade = getattr(self.config.risk, 'max_risk_per_trade', 0.02)
            
            # Calculate risk amount in currency
            risk_amount = account_balance * risk_per_trade
            
            # Calculate price distance to stop loss
            price_distance = abs(entry_price - stop_loss)
            
            if price_distance == 0:
                logger.warning("Stop loss distance is zero, using minimum volume")
                return getattr(self.config.trading, 'min_volume', 0.01)
            
            # Basic position size calculation
            basic_volume = risk_amount / price_distance
            
            # Apply Kelly Criterion adjustment
            kelly_volume = self._apply_kelly_criterion(basic_volume, account_balance)
            
            # Apply portfolio risk limits
            portfolio_adjusted_volume = self._apply_portfolio_limits(kelly_volume, account_balance)
            
            # Apply consecutive loss adjustment
            loss_adjusted_volume = self._apply_consecutive_loss_adjustment(portfolio_adjusted_volume)
            
            # Ensure volume is within trading limits
            min_volume = getattr(self.config.trading, 'min_volume', 0.01)
            max_volume = getattr(self.config.trading, 'max_volume', 1.0)
            
            final_volume = max(min_volume, min(max_volume, loss_adjusted_volume))
            
            logger.debug(f"Position size calculation: Basic={basic_volume:.3f}, "
                        f"Kelly={kelly_volume:.3f}, Final={final_volume:.3f}")
            
            return round(final_volume, 3)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return getattr(self.config.trading, 'min_volume', 0.01)
    
    def _apply_kelly_criterion(self, basic_volume: float, account_balance: float) -> float:
        """Apply Kelly Criterion for optimal position sizing"""
        try:
            # For Kelly Criterion, we need win rate and average win/loss
            # Since we don't have historical data here, we'll use conservative estimates
            # In a real implementation, this would use actual trading history
            
            estimated_win_rate = 0.55  # Conservative estimate
            estimated_avg_win = 0.015  # 1.5% average win
            estimated_avg_loss = 0.012  # 1.2% average loss
            
            if estimated_avg_loss == 0:
                return basic_volume
            
            # Kelly formula: f = (bp - q) / b
            # where b = odds (avg_win/avg_loss), p = win_rate, q = loss_rate
            b = estimated_avg_win / estimated_avg_loss
            p = estimated_win_rate
            q = 1 - p
            
            kelly_fraction_optimal = (b * p - q) / b
            
            # Apply conservative Kelly fraction (typically 25% of optimal)
            kelly_fraction_used = kelly_fraction_optimal * self.kelly_fraction
            
            # Ensure Kelly fraction is reasonable (between 0 and 0.5)
            kelly_fraction_used = max(0, min(0.5, kelly_fraction_used))
            
            # Apply Kelly adjustment
            kelly_volume = basic_volume * (1 + kelly_fraction_used)
            
            return kelly_volume
            
        except Exception as e:
            logger.error(f"Error applying Kelly Criterion: {e}")
            return basic_volume
    
    def _apply_portfolio_limits(self, volume: float, account_balance: float) -> float:
        """Apply portfolio-level risk limits"""
        try:
            # Calculate maximum volume based on portfolio risk
            max_portfolio_risk_amount = account_balance * self.max_portfolio_risk
            
            # Assuming average risk per position (this is simplified)
            avg_risk_per_position = account_balance * getattr(self.config.risk, 'max_risk_per_trade', 0.02)
            max_positions = getattr(self.config.risk, 'max_positions', 3)
            
            # Calculate maximum volume to stay within portfolio limits
            max_volume_portfolio = max_portfolio_risk_amount / max_positions / avg_risk_per_position * volume
            
            return min(volume, max_volume_portfolio)
            
        except Exception as e:
            logger.error(f"Error applying portfolio limits: {e}")
            return volume
    
    def _apply_consecutive_loss_adjustment(self, volume: float) -> float:
        """Reduce position size after consecutive losses"""
        try:
            if self.consecutive_losses == 0:
                return volume
            
            # Reduce volume by 10% for each consecutive loss, max 50% reduction
            reduction_factor = min(0.5, self.consecutive_losses * 0.1)
            adjusted_volume = volume * (1 - reduction_factor)
            
            logger.debug(f"Consecutive loss adjustment: {self.consecutive_losses} losses, "
                        f"reduction: {reduction_factor:.1%}, volume: {volume:.3f} -> {adjusted_volume:.3f}")
            
            return adjusted_volume
            
        except Exception as e:
            logger.error(f"Error applying consecutive loss adjustment: {e}")
            return volume
    
    def calculate_dynamic_stop_loss(self, entry_price: float, direction: str, 
                                  atr: float, market_volatility: str = "normal") -> float:
        """Calculate dynamic stop loss based on ATR and market conditions"""
        try:
            base_multiplier = self.stop_loss_atr_multiplier
            
            # Adjust multiplier based on market volatility
            if market_volatility == "high":
                multiplier = base_multiplier * 1.3
            elif market_volatility == "low":
                multiplier = base_multiplier * 0.8
            else:
                multiplier = base_multiplier
            
            # Calculate stop loss
            if direction.lower() == "buy" or direction.lower() == "long":
                stop_loss = entry_price - (atr * multiplier)
            else:  # sell or short
                stop_loss = entry_price + (atr * multiplier)
            
            return stop_loss
            
        except Exception as e:
            logger.error(f"Error calculating dynamic stop loss: {e}")
            # Fallback to basic calculation
            if direction.lower() in ["buy", "long"]:
                return entry_price * 0.99  # 1% stop loss
            else:
                return entry_price * 1.01
    
    def calculate_trailing_stop(self, position, market_state) -> Optional[float]:
        """Calculate trailing stop loss"""
        try:
            if not self.trailing_stop_enabled:
                return None
            
            current_price = market_state.price
            atr = market_state.atr_data.atr
            
            # Calculate trailing distance
            trailing_distance = atr * self.stop_loss_atr_multiplier
            
            if position.type == 0:  # Long position
                new_stop = current_price - trailing_distance
                # Only move stop loss up, never down
                if new_stop > position.sl:
                    return new_stop
            else:  # Short position
                new_stop = current_price + trailing_distance
                # Only move stop loss down, never up
                if new_stop < position.sl:
                    return new_stop
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating trailing stop: {e}")
            return None
    
    def validate_trade(self, signal, current_positions: List, account_info: Dict) -> Tuple[bool, str]:
        """Enhanced trade validation"""
        try:
            # Basic validation from parent class
            is_valid, reason = super().validate_trade(signal, current_positions, account_info)
            
            if not is_valid:
                return False, reason
            
            # Enhanced validations
            
            # 1. Check consecutive losses
            if self.consecutive_losses >= self.max_consecutive_losses:
                return False, f"Too many consecutive losses: {self.consecutive_losses}"
            
            # 2. Check daily loss limit
            if self._check_daily_loss_limit(account_info):
                return False, "Daily loss limit reached"
            
            # 3. Check portfolio risk
            if self._check_portfolio_risk(signal, current_positions, account_info):
                return False, "Portfolio risk limit exceeded"
            
            # 4. Check correlation (simplified)
            if self._check_position_correlation(signal, current_positions):
                return False, "Too many correlated positions"
            
            return True, "Trade approved"
            
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return False, f"Validation error: {e}"
    
    def should_close_position(self, position, market_state, account_info: Dict) -> Tuple[bool, str]:
        """Enhanced position closing logic"""
        try:
            # Basic checks from parent class
            should_close, reason = super().should_close_position(position, market_state.price, market_state.atr_data.atr)
            
            if should_close:
                return True, reason
            
            # Enhanced closing criteria
            
            # 1. Market regime change
            if hasattr(market_state, 'market_regime'):
                regime_change = self._check_regime_change(position, market_state.market_regime)
                if regime_change:
                    return True, "Market regime changed"
            
            # 2. Volatility spike
            if hasattr(market_state, 'atr_data'):
                if market_state.atr_data.volatility_level == "very_high":
                    return True, "Extreme volatility detected"
            
            # 3. Time-based exit (weekend approach)
            if self._should_close_for_weekend():
                return True, "Weekend approach - closing positions"
            
            return False, ""
            
        except Exception as e:
            logger.error(f"Error checking position close: {e}")
            return False, ""
    
    def _check_daily_loss_limit(self, account_info: Dict) -> bool:
        """Check if daily loss limit is reached"""
        try:
            # This would need to track daily P&L
            # For now, return False (not implemented)
            return False
        except:
            return False
    
    def _check_portfolio_risk(self, signal, current_positions: List, account_info: Dict) -> bool:
        """Check if adding this position would exceed portfolio risk"""
        try:
            # Calculate current portfolio risk
            current_risk = 0
            for pos in current_positions:
                # Simplified risk calculation
                position_risk = abs(pos.price_open - pos.sl) * pos.volume
                current_risk += position_risk
            
            # Calculate new position risk
            new_position_risk = abs(signal.entry_price - signal.stop_loss) * signal.volume
            
            # Check against portfolio limit
            max_portfolio_risk = account_info['balance'] * self.max_portfolio_risk
            
            return (current_risk + new_position_risk) > max_portfolio_risk
            
        except Exception as e:
            logger.error(f"Error checking portfolio risk: {e}")
            return False
    
    def _check_position_correlation(self, signal, current_positions: List) -> bool:
        """Check if too many positions in same direction"""
        try:
            same_direction_count = 0
            
            for pos in current_positions:
                if ((signal.action == 'buy' and pos.type == 0) or 
                    (signal.action == 'sell' and pos.type == 1)):
                    same_direction_count += 1
            
            # Allow max 2 positions in same direction
            return same_direction_count >= 2
            
        except Exception as e:
            logger.error(f"Error checking position correlation: {e}")
            return False
    
    def _check_regime_change(self, position, market_regime) -> bool:
        """Check if market regime changed against position"""
        try:
            # Long position in bearish regime
            if position.type == 0 and 'bearish' in market_regime.trend_direction.lower():
                return True
            
            # Short position in bullish regime
            if position.type == 1 and 'bullish' in market_regime.trend_direction.lower():
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking regime change: {e}")
            return False
    
    def _should_close_for_weekend(self) -> bool:
        """Check if positions should be closed for weekend"""
        try:
            now = datetime.now()
            
            # Close positions on Friday after 15:00
            if now.weekday() == 4 and now.hour >= 15:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking weekend close: {e}")
            return False
    
    def update_consecutive_losses(self, trade_result: str):
        """Update consecutive losses counter"""
        try:
            if trade_result == "loss":
                self.consecutive_losses += 1
            elif trade_result == "win":
                self.consecutive_losses = 0
                
            logger.debug(f"Consecutive losses updated: {self.consecutive_losses}")
            
        except Exception as e:
            logger.error(f"Error updating consecutive losses: {e}")
    
    def get_risk_summary(self) -> Dict:
        """Get current risk summary"""
        return {
            'consecutive_losses': self.consecutive_losses,
            'max_consecutive_losses': self.max_consecutive_losses,
            'kelly_fraction': self.kelly_fraction,
            'max_portfolio_risk': self.max_portfolio_risk,
            'trailing_stop_enabled': self.trailing_stop_enabled,
            'stop_loss_atr_multiplier': self.stop_loss_atr_multiplier,
            'take_profit_atr_multiplier': self.take_profit_atr_multiplier
        }

# Volume Analysis

## Tổng quan

Tính năng phân tích khối lượng giao dịch giúp bạn hiểu rõ mối quan hệ giữa position sizing (khối lượng giao dịch) và hiệu suất, từ đó tối ưu hóa chiến lược quản lý rủi ro và position sizing.

## Các biểu đồ được tạo

### 1. Volume Analysis (`volume_analysis.png`)
Biểu đồ tổng hợp gồm 4 phần:

- **Volume Distribution per Trade**: Phân phối khối lượng giao dịch với đường trung bình
- **Volume vs P&L Relationship**: <PERSON><PERSON><PERSON><PERSON> đồ scatter thể hiện mối quan hệ giữa khối lượng và P&L
- **Average Volume by Hour of Day**: Khối lượng trung bình theo từng giờ trong ngày
- **Daily Total Volume Timeline**: <PERSON><PERSON><PERSON><PERSON> đồ đường thể hiện tổng khối lượng mỗi ngày

### 2. Volume Performance Analysis (`volume_performance_analysis.png`)
Phân tích hiệu suất theo nhóm khối lượng:

- **Total P&L by Volume Bucket**: Tổng P&L theo các nhóm khối lượng (Very Low, Low, Medium, High, Very High)
- **Average P&L by Volume Bucket**: P&L trung bình theo các nhóm khối lượng

### 3. Volume Duration Analysis (`volume_duration_analysis.png`)
Phân tích mối quan hệ khối lượng và thời gian:

- **Volume vs Trade Duration**: Biểu đồ scatter với màu sắc theo P&L
- **Average Volume by Day of Week**: Khối lượng trung bình theo từng ngày trong tuần

## Thống kê tóm tắt trong HTML Report

HTML report sẽ hiển thị bảng **Volume Analysis** với các thông tin:

- **Total Volume Traded**: Tổng khối lượng đã giao dịch
- **Average Volume per Trade**: Khối lượng trung bình mỗi giao dịch
- **Maximum Volume per Trade**: Khối lượng lớn nhất trong một giao dịch
- **Minimum Volume per Trade**: Khối lượng nhỏ nhất trong một giao dịch
- **Volume Standard Deviation**: Độ lệch chuẩn của khối lượng
- **Volume per Day (Avg)**: Khối lượng trung bình mỗi ngày

## Cách sử dụng

### 1. Chạy backtest bình thường
```python
from src.backtesting.backtest_engine import BacktestEngine, BacktestReporter
from src.utils.config import BotConfig

# Load config
config = BotConfig.from_file('config/config.yaml')

# Create engine
engine = BacktestEngine(config)

# Run backtest
results = engine.run_backtest(data)

# Create reporter
reporter = BacktestReporter()

# Generate visualizations (bao gồm volume analysis)
reporter.create_visualizations(results, save_dir="reports")

# Generate HTML report
reporter.generate_report(results, save_path="reports/backtest_report.html")
```

## Ý nghĩa của phân tích

### 1. Tối ưu hóa Position Sizing
- Xác định khối lượng tối ưu cho mỗi giao dịch
- Hiểu rõ mối quan hệ giữa khối lượng và rủi ro
- Điều chỉnh position sizing theo điều kiện thị trường

### 2. Quản lý rủi ro
- Phân tích mối quan hệ khối lượng và drawdown
- Tối ưu hóa risk per trade
- Điều chỉnh khối lượng theo volatility

### 3. Cải thiện chiến lược
- Thêm bộ lọc khối lượng vào chiến lược
- Tối ưu hóa position sizing theo thời gian
- Phát triển chiến lược đa khối lượng

## Ví dụ phân tích

### Kết quả mẫu:
```
Total Volume Traded: 1,629.00 lots
Average Volume per Trade: 1.00 lots
Maximum Volume per Trade: 1.00 lots
Minimum Volume per Trade: 1.00 lots
Volume Standard Deviation: 0.00 lots
Volume per Day (Avg): 26.70 lots/day
```

### Diễn giải:
- **Khối lượng cố định**: Tất cả giao dịch đều sử dụng 1.00 lot (có thể do cấu hình cố định)
- **Khối lượng hàng ngày**: Trung bình 26.70 lots/ngày cho thấy hoạt động giao dịch tích cực
- **Độ lệch chuẩn 0**: Cho thấy khối lượng không thay đổi (position sizing cố định)

## Lợi ích của Volume Analysis

### 1. Tối ưu hóa Position Sizing
- **Dynamic Sizing**: Điều chỉnh khối lượng theo volatility
- **Risk Management**: Kiểm soát rủi ro theo khối lượng
- **Performance Optimization**: Tối ưu hóa hiệu suất theo khối lượng

### 2. Phân tích Mối quan hệ
- **Volume vs P&L**: Hiểu rõ mối quan hệ giữa khối lượng và lợi nhuận
- **Volume vs Duration**: Phân tích thời gian giao dịch theo khối lượng
- **Volume vs Time**: Hiểu rõ pattern khối lượng theo thời gian

### 3. Quản lý Rủi ro
- **Risk per Trade**: Kiểm soát rủi ro mỗi giao dịch
- **Portfolio Risk**: Quản lý rủi ro tổng thể
- **Drawdown Control**: Kiểm soát drawdown theo khối lượng

## Tùy chỉnh

Bạn có thể tùy chỉnh phân tích bằng cách:

1. **Thay đổi volume buckets**: Sửa đổi trong `_create_detailed_volume_analysis()`
2. **Thêm chỉ báo**: Bổ sung các chỉ báo khác như Kelly Criterion, Optimal F
3. **Tùy chỉnh màu sắc**: Thay đổi colormap trong matplotlib
4. **Thêm bộ lọc**: Lọc theo symbol, direction, hoặc các điều kiện khác

## Lưu ý

1. **Dữ liệu đủ**: Cần có đủ dữ liệu giao dịch để phân tích có ý nghĩa
2. **Position Sizing**: Phân tích dựa trên khối lượng thực tế được sử dụng
3. **Risk Management**: Luôn kết hợp với quản lý rủi ro tổng thể
4. **Market Conditions**: Điều chỉnh theo điều kiện thị trường thay đổi

## Kết luận

Volume Analysis là công cụ mạnh mẽ để:
- Tối ưu hóa position sizing
- Cải thiện quản lý rủi ro
- Tăng hiệu suất giao dịch
- Hiểu rõ mối quan hệ giữa khối lượng và hiệu suất

Sử dụng kết hợp với Time Analysis và các chỉ báo khác để có cái nhìn toàn diện về chiến lược giao dịch.
#!/usr/bin/env python3
"""
Virtual Environment Setup Script for MT5 Trading Bot
Tự động tạo và cấu hình virtual environment
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_header():
    """Print welcome header"""
    print("=" * 70)
    print("🐍 MT5 TRADING BOT - VIRTUAL ENVIRONMENT SETUP")
    print("🔧 Tự động tạo và cấu hình môi trường ảo")
    print("=" * 70)


def detect_python_command():
    """Detect available Python command"""
    python_commands = ['python3', 'python', 'py']

    for cmd in python_commands:
        try:
            result = subprocess.run([cmd, '--version'],
                                  capture_output=True, text=True, check=True)
            version_line = result.stdout.strip()
            print(f"✅ Tìm thấy Python: {cmd} - {version_line}")

            # Check version
            version_parts = version_line.split()[1].split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])

            if major >= 3 and minor >= 8:
                return cmd
            else:
                print(f"⚠️  {cmd} phiên bản quá cũ (cần >= 3.8)")

        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ Không tìm thấy: {cmd}")

    return None


def check_existing_venv():
    """Check if virtual environment already exists"""
    venv_path = Path("venv")
    if venv_path.exists():
        print(f"⚠️  Virtual environment đã tồn tại: {venv_path.absolute()}")

        while True:
            choice = input("Bạn muốn (r)ecreate, (u)se existing, hay (q)uit? [r/u/q]: ").lower()
            if choice == 'r':
                print("🗑️  Xóa virtual environment cũ...")
                import shutil
                shutil.rmtree(venv_path)
                return False
            elif choice == 'u':
                return True
            elif choice == 'q':
                print("👋 Thoát...")
                sys.exit(0)
            else:
                print("❌ Vui lòng chọn r, u, hoặc q")

    return False


def create_virtual_environment(python_cmd):
    """Create virtual environment"""
    print(f"\n🔨 Tạo virtual environment với {python_cmd}...")

    try:
        subprocess.run([python_cmd, '-m', 'venv', 'venv'], check=True)
        print("✅ Virtual environment đã được tạo thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi tạo virtual environment: {e}")

        # Try alternative method
        print("🔄 Thử phương pháp khác...")
        try:
            subprocess.run([python_cmd, '-m', 'pip', 'install', 'virtualenv'], check=True)
            subprocess.run([python_cmd, '-m', 'virtualenv', 'venv'], check=True)
            print("✅ Virtual environment đã được tạo với virtualenv!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Không thể tạo virtual environment")
            return False


def get_activation_command():
    """Get activation command for current OS"""
    system = platform.system().lower()

    if system == "windows":
        return "venv\\Scripts\\activate"
    else:
        return "source venv/bin/activate"


def install_dependencies():
    """Install dependencies in virtual environment"""
    print("\n📦 Cài đặt dependencies...")

    system = platform.system().lower()

    if system == "windows":
        pip_path = "venv\\Scripts\\pip"
        python_path = "venv\\Scripts\\python"
    else:
        pip_path = "venv/bin/pip"
        python_path = "venv/bin/python"

    try:
        # Upgrade pip first
        print("🔄 Cập nhật pip...")
        subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)

        # Choose appropriate requirements file
        if system == "darwin":  # macOS
            requirements_file = "requirements-macos.txt"
            print("🍎 Phát hiện macOS - sử dụng requirements-macos.txt")
        else:
            requirements_file = "requirements.txt"

        # Install requirements
        if Path(requirements_file).exists():
            print(f"📋 Cài đặt từ {requirements_file}...")
            subprocess.run([pip_path, 'install', '-r', requirements_file], check=True)

            # Special handling for macOS TA-Lib
            if system == "darwin":
                print("🔧 Cài đặt TA-Lib cho macOS...")
                try:
                    subprocess.run([pip_path, 'install', 'TA-Lib'], check=True)
                    print("✅ TA-Lib đã được cài đặt!")
                except subprocess.CalledProcessError:
                    print("⚠️  TA-Lib cài đặt thất bại. Chạy lệnh sau để cài đặt thủ công:")
                    print("   brew install ta-lib")
                    print("   pip install TA-Lib")

            print("✅ Dependencies đã được cài đặt!")
        else:
            print(f"⚠️  Không tìm thấy {requirements_file}")

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi cài đặt dependencies: {e}")
        if system == "darwin":
            print("💡 Gợi ý cho macOS:")
            print("   1. Cài đặt Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            print("   2. Cài đặt TA-Lib: brew install ta-lib")
            print("   3. Chạy lại script này")
        return False


def test_installation():
    """Test the installation"""
    print("\n🧪 Kiểm tra cài đặt...")

    system = platform.system().lower()
    python_path = "venv\\Scripts\\python" if system == "windows" else "venv/bin/python"

    test_imports = [
        "import sys; print(f'Python: {sys.version}')",
        "import pandas; print(f'Pandas: {pandas.__version__}')",
        "import numpy; print(f'Numpy: {numpy.__version__}')",
        "import MetaTrader5; print('MetaTrader5: OK')",
    ]

    for test in test_imports:
        try:
            result = subprocess.run([python_path, '-c', test],
                                  capture_output=True, text=True, check=True)
            print(f"✅ {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            print(f"❌ Lỗi: {test.split(';')[0]}")


def create_activation_scripts():
    """Create convenient activation scripts"""
    print("\n📝 Tạo scripts kích hoạt...")

    system = platform.system().lower()

    if system == "windows":
        # Windows batch file
        script_content = """@echo off
cd /d "%~dp0"
call venv\\Scripts\\activate.bat
echo ✅ Virtual environment activated!
echo 📍 Project: %CD%
echo 🐍 Python:
where python
echo 📦 Pip:
where pip
echo.
echo 🚀 Sẵn sàng chạy bot:
echo    python main.py
echo    python run_tests.py
echo    python run_backtest.py
cmd /k
"""
        with open("activate_env.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ Tạo activate_env.bat")

    else:
        # Unix shell script
        script_content = """#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
echo "✅ Virtual environment activated!"
echo "📍 Project: $(pwd)"
echo "🐍 Python: $(which python)"
echo "📦 Pip: $(which pip)"
echo ""
echo "🚀 Sẵn sàng chạy bot:"
echo "   python main.py"
echo "   python run_tests.py"
echo "   python run_backtest.py"
echo ""
echo "💡 Để tắt virtual environment: deactivate"
exec "$SHELL"
"""
        with open("activate_env.sh", "w", encoding="utf-8") as f:
            f.write(script_content)

        # Make executable
        os.chmod("activate_env.sh", 0o755)
        print("✅ Tạo activate_env.sh")


def show_next_steps():
    """Show next steps"""
    system = platform.system().lower()
    activation_cmd = get_activation_command()

    print("\n" + "=" * 70)
    print("🎉 SETUP HOÀN THÀNH!")
    print("=" * 70)

    print("\n🚀 CÁCH SỬ DỤNG:")
    print("-" * 40)

    if system == "windows":
        print("1. Kích hoạt virtual environment:")
        print(f"   {activation_cmd}")
        print("   HOẶC double-click: activate_env.bat")
    else:
        print("1. Kích hoạt virtual environment:")
        print(f"   {activation_cmd}")
        print("   HOẶC chạy: ./activate_env.sh")

    print("\n2. Chạy các lệnh:")
    print("   python main.py          # Chạy bot")
    print("   python run_tests.py     # Chạy tests")
    print("   python run_backtest.py  # Chạy backtest")
    print("   python quick_start.py   # Setup và kiểm tra")

    print("\n3. Tắt virtual environment:")
    print("   deactivate")

    print("\n📚 TÀI LIỆU:")
    print("-" * 40)
    print("• Hướng dẫn chi tiết: docs/VIRTUAL_ENVIRONMENT_GUIDE.md")
    print("• API Reference: docs/API_REFERENCE.md")
    print("• README: README.md")

    print("\n⚠️  LƯU Ý:")
    print("-" * 40)
    print("• Luôn kích hoạt virtual environment trước khi làm việc")
    print("• Cấu hình MT5 credentials trong config/config.yaml")
    print("• Test trong demo mode trước khi live trading")


def main():
    """Main setup function"""
    print_header()

    # Step 1: Detect Python
    python_cmd = detect_python_command()
    if not python_cmd:
        print("\n❌ Không tìm thấy Python phù hợp!")
        print("\n📥 HƯỚNG DẪN CÀI ĐẶT PYTHON:")
        print("• macOS: brew install python3")
        print("• Ubuntu/Debian: sudo apt-get install python3.8")
        print("• Windows: Download từ https://python.org")
        return 1

    # Step 2: Check existing venv
    venv_exists = check_existing_venv()

    # Step 3: Create venv if needed
    if not venv_exists:
        if not create_virtual_environment(python_cmd):
            return 1

    # Step 4: Install dependencies
    if not install_dependencies():
        print("⚠️  Có lỗi khi cài đặt dependencies, nhưng venv đã được tạo")

    # Step 5: Test installation
    test_installation()

    # Step 6: Create activation scripts
    create_activation_scripts()

    # Step 7: Show next steps
    show_next_steps()

    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Setup bị hủy bởi người dùng")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Lỗi không mong đợi: {e}")
        sys.exit(1)

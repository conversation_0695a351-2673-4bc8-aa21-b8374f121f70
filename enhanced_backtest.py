#!/usr/bin/env python3
"""
Enhanced Backtesting Script
Simplified interface for comprehensive backtesting with advanced metrics
"""

import sys
import argparse
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.backtesting.backtest_engine import BacktestEngine, SimpleBacktestConfig
from src.utils.config import load_config
from src.utils.logger import get_logger

logger = get_logger(__name__)


def print_results_summary(results):
    """Print a comprehensive results summary"""
    print("\n" + "="*80)
    print("🎯 ENHANCED BACKTEST RESULTS SUMMARY")
    print("="*80)
    
    # Basic Performance
    print(f"\n📊 BASIC PERFORMANCE:")
    print(f"  Period: {results.start_date.strftime('%Y-%m-%d')} to {results.end_date.strftime('%Y-%m-%d')}")
    print(f"  Initial Balance: ${results.initial_balance:,.2f}")
    print(f"  Final Balance: ${results.final_balance:,.2f}")
    print(f"  Total Return: ${results.total_return:,.2f} ({results.total_return_pct:.2%})")
    print(f"  Total Trades: {results.total_trades}")
    
    # Trade Statistics
    print(f"\n📈 TRADE STATISTICS:")
    print(f"  Win Rate: {results.win_rate:.2%}")
    print(f"  Winning Trades: {results.winning_trades}")
    print(f"  Losing Trades: {results.losing_trades}")
    print(f"  Average Win: ${results.avg_win:.2f}")
    print(f"  Average Loss: ${results.avg_loss:.2f}")
    print(f"  Profit Factor: {results.profit_factor:.2f}")
    print(f"  Expectancy: ${results.expectancy:.2f}")
    
    # Risk Metrics
    print(f"\n⚠️  RISK METRICS:")
    print(f"  Max Drawdown: ${results.max_drawdown:.2f} ({results.max_drawdown_pct:.2%})")
    print(f"  Sharpe Ratio: {results.sharpe_ratio:.2f}")
    print(f"  Sortino Ratio: {results.sortino_ratio:.2f}")
    print(f"  Calmar Ratio: {results.calmar_ratio:.2f}")
    print(f"  VaR (95%): {results.var_95:.4f}")
    print(f"  CVaR (95%): {results.cvar_95:.4f}")
    print(f"  Ulcer Index: {results.ulcer_index:.4f}")
    
    # Advanced Metrics
    print(f"\n🎯 ADVANCED METRICS:")
    print(f"  Kelly Criterion: {results.kelly_criterion:.2%}")
    print(f"  Recovery Factor: {results.recovery_factor:.2f}")
    print(f"  Information Ratio: {results.information_ratio:.2f}")
    print(f"  Max Consecutive Wins: {results.max_consecutive_wins}")
    print(f"  Max Consecutive Losses: {results.max_consecutive_losses}")
    print(f"  Average Trade Duration: {results.avg_trade_duration:.1f} minutes")
    
    # Volume Statistics
    print(f"\n📦 VOLUME STATISTICS:")
    print(f"  Total Volume: {results.total_volume:.2f}")
    print(f"  Average Volume per Trade: {results.avg_volume_per_trade:.3f}")
    print(f"  Max Volume per Trade: {results.max_volume_per_trade:.3f}")
    print(f"  Min Volume per Trade: {results.min_volume_per_trade:.3f}")
    
    # Performance by Market Condition
    if results.performance_by_condition:
        print(f"\n🌍 PERFORMANCE BY MARKET CONDITION:")
        for condition, perf in results.performance_by_condition.items():
            print(f"  {condition.title()}: {perf['trades']} trades, "
                  f"Win Rate: {perf['win_rate']:.1%}, "
                  f"Avg PnL: ${perf['avg_pnl']:.2f}")
    
    # Multi-timeframe Analysis
    if results.timeframe_analysis:
        print(f"\n⏰ MULTI-TIMEFRAME ANALYSIS:")
        for tf, analysis in results.timeframe_analysis.items():
            if 'error' not in analysis:
                print(f"  {tf}: {analysis['total_trades']} trades, "
                      f"Win Rate: {analysis['win_rate']:.1%}, "
                      f"Return: {analysis['total_return_pct']:.1%}")
    
    # Trade Timing
    if results.trade_timing_stats:
        print(f"\n🕐 TRADE TIMING ANALYSIS:")
        print(f"  Best Trading Hour: {results.trade_timing_stats.get('best_hour', 'N/A')}")
        print(f"  Best Trading Day: {results.trade_timing_stats.get('best_day', 'N/A')}")
    
    # Performance Rating
    rating = get_performance_rating(results)
    print(f"\n⭐ OVERALL PERFORMANCE RATING: {rating}")
    
    print("="*80)


def get_performance_rating(results):
    """Calculate overall performance rating"""
    score = 0
    
    # Win rate (25 points max)
    if results.win_rate >= 0.6:
        score += 25
    elif results.win_rate >= 0.5:
        score += 20
    elif results.win_rate >= 0.4:
        score += 15
    else:
        score += 10
    
    # Profit factor (25 points max)
    if results.profit_factor >= 2.0:
        score += 25
    elif results.profit_factor >= 1.5:
        score += 20
    elif results.profit_factor >= 1.2:
        score += 15
    elif results.profit_factor >= 1.0:
        score += 10
    else:
        score += 0
    
    # Sharpe ratio (25 points max)
    if results.sharpe_ratio >= 2.0:
        score += 25
    elif results.sharpe_ratio >= 1.5:
        score += 20
    elif results.sharpe_ratio >= 1.0:
        score += 15
    elif results.sharpe_ratio >= 0.5:
        score += 10
    else:
        score += 0
    
    # Max drawdown (25 points max)
    if results.max_drawdown_pct <= 0.05:
        score += 25
    elif results.max_drawdown_pct <= 0.10:
        score += 20
    elif results.max_drawdown_pct <= 0.15:
        score += 15
    elif results.max_drawdown_pct <= 0.20:
        score += 10
    else:
        score += 0
    
    # Convert to rating
    if score >= 90:
        return "🌟 EXCELLENT (A+)"
    elif score >= 80:
        return "⭐ VERY GOOD (A)"
    elif score >= 70:
        return "✨ GOOD (B+)"
    elif score >= 60:
        return "⚡ FAIR (B)"
    elif score >= 50:
        return "💫 POOR (C)"
    else:
        return "💥 VERY POOR (D)"


def main():
    """Main backtesting function"""
    parser = argparse.ArgumentParser(description="Enhanced Backtesting with Simplified Configuration")
    parser.add_argument("--period", default="1month", 
                       choices=["1week", "1month", "3months", "6months", "1year"],
                       help="Backtest period")
    parser.add_argument("--balance", type=float, default=10000.0,
                       help="Initial balance")
    parser.add_argument("--start-date", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", help="End date (YYYY-MM-DD)")
    parser.add_argument("--symbol", default="XAUUSD", help="Trading symbol")
    parser.add_argument("--multi-timeframe", action="store_true",
                       help="Enable multi-timeframe analysis")
    
    args = parser.parse_args()
    
    try:
        print("🚀 Starting Enhanced Backtesting...")
        
        # Load configuration
        config = load_config()
        
        # Create backtest engine
        engine = BacktestEngine(config)
        
        # Setup backtest configuration
        if args.start_date and args.end_date:
            # Custom date range
            simple_config = SimpleBacktestConfig(
                start_date=args.start_date,
                end_date=args.end_date,
                initial_balance=args.balance
            )
        else:
            # Quick setup
            simple_config = SimpleBacktestConfig.quick_setup(
                period=args.period,
                balance=args.balance
            )
        
        # Add multi-timeframe analysis if requested
        if args.multi_timeframe:
            simple_config.timeframes = ['M5', 'M15', 'H1']
        
        print(f"📅 Backtest Period: {simple_config.start_date} to {simple_config.end_date}")
        print(f"💰 Initial Balance: ${simple_config.initial_balance:,.2f}")
        print(f"📊 Symbol: {args.symbol}")
        print(f"⏰ Timeframes: {simple_config.timeframes}")
        
        # Run backtest
        results = engine.run_simple_backtest(simple_config, args.symbol)
        
        # Print results
        print_results_summary(results)
        
        # Generate report
        engine.generate_report(results)
        
        print(f"\n📄 Detailed report saved to: reports/")
        print("✅ Enhanced backtesting completed successfully!")
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        print(f"❌ Backtest failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/bin/bash

echo "Starting MT5 Python Trading Bot Scheduler..."
echo ""
echo "This will run the bot according to the following schedule:"
echo "- Monday 00:00 to Tuesday 23:59"
echo "- Friday 00:00 to Friday 23:59"
echo ""
echo "Press Ctrl+C to stop the scheduler"
echo ""

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    echo "Virtual environment activated."
else
    echo "Virtual environment not found. Using system Python."
fi

# Run the scheduler
python scheduler.py

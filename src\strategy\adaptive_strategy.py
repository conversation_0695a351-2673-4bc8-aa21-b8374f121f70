"""
Adaptive Strategy Module
<PERSON><PERSON><PERSON> lư<PERSON> thích <PERSON>ng với điều kiện thị trường
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import numpy as np

from .gold_strategy import TradingSignal, MarketState
from ..indicators.technical_indicators import TechnicalIndicators
from ..risk.enhanced_risk_manager import EnhancedRiskManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class AdaptiveStrategy:
    """Chiến lược thích ứng với điều kiện thị trường"""
    
    def __init__(self, config):
        self.config = config
        self.indicators = TechnicalIndicators(config)
        self.risk_manager = EnhancedRiskManager(config)
        
        # Adaptive parameters
        self.adaptive_params = {
            'trending_multiplier': getattr(config.adaptive_strategy, 'trending_multiplier', 1.5),
            'ranging_multiplier': getattr(config.adaptive_strategy, 'ranging_multiplier', 0.8),
            'volatility_adjustment': getattr(config.adaptive_strategy, 'volatility_adjustment', True),
            'time_filters': getattr(config.adaptive_strategy, 'time_filters', {})
        }
        
        logger.info("Adaptive Strategy initialized")
    
    def generate_adaptive_signal(self, market_state: MarketState, 
                                current_positions: List, 
                                account_info: Dict) -> Optional[TradingSignal]:
        """Generate adaptive trading signal based on market conditions"""
        try:
            # Check if trading is allowed based on time and market conditions
            if not self.is_good_trading_time(datetime.now()):
                logger.debug("Trading not allowed at current time")
                return None
            
            # Get adaptive parameters based on market regime
            adaptive_params = self.get_adaptive_parameters(
                market_state.market_regime.market_phase,
                market_state.market_regime.volatility_regime
            )
            
            # Generate base signal using enhanced indicators
            base_signal = self._generate_base_signal(market_state, adaptive_params)
            
            if not base_signal:
                return None
            
            # Apply adaptive adjustments
            adaptive_signal = self._apply_adaptive_adjustments(
                base_signal, market_state, adaptive_params, account_info
            )
            
            # Final validation
            if self._validate_adaptive_signal(adaptive_signal, current_positions, market_state):
                return adaptive_signal
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error generating adaptive signal: {e}")
            return None
    
    def get_adaptive_parameters(self, market_condition: str, volatility_level: str) -> Dict:
        """Get adaptive parameters based on market conditions"""
        base_params = {
            'signal_threshold': getattr(self.config.strategy_scoring, 'min_signal_strength', 60),
            'position_size_multiplier': 1.0,
            'stop_loss_multiplier': 1.0,
            'take_profit_multiplier': 1.0,
            'max_positions': getattr(self.config.risk, 'max_positions', 3)
        }
        
        # Adjust for market condition
        if 'trending' in market_condition.lower():
            base_params['position_size_multiplier'] = self.adaptive_params['trending_multiplier']
            base_params['signal_threshold'] *= 0.9  # Lower threshold for trending markets
            base_params['take_profit_multiplier'] = 1.2  # Larger TP in trending markets
            
        elif 'ranging' in market_condition.lower():
            base_params['position_size_multiplier'] = self.adaptive_params['ranging_multiplier']
            base_params['signal_threshold'] *= 1.1  # Higher threshold for ranging markets
            base_params['stop_loss_multiplier'] = 0.8  # Tighter SL in ranging markets
        
        # Adjust for volatility
        if volatility_level == 'high':
            base_params['stop_loss_multiplier'] *= 1.3  # Wider stops in high volatility
            base_params['position_size_multiplier'] *= 0.8  # Smaller positions
            
        elif volatility_level == 'low':
            base_params['stop_loss_multiplier'] *= 0.9  # Tighter stops in low volatility
            base_params['position_size_multiplier'] *= 1.1  # Larger positions
        
        return base_params
    
    def is_good_trading_time(self, current_time: datetime, market_session: str = "auto") -> bool:
        """Check if current time is good for trading"""
        try:
            hour = current_time.hour
            weekday = current_time.weekday()  # 0=Monday, 6=Sunday
            
            # Don't trade on weekends
            if weekday >= 5:  # Saturday or Sunday
                return False
            
            # Don't trade during very low liquidity hours (typically 22:00-01:00 GMT)
            if hour >= 22 or hour <= 1:
                return False
            
            # Apply session-specific filters
            time_filters = self.adaptive_params.get('time_filters', {})
            
            # London session boost (8:00-17:00 GMT)
            if 8 <= hour <= 17 and time_filters.get('london_session_boost', False):
                return True
            
            # Reduce activity during Asian session (1:00-8:00 GMT)
            if 1 <= hour <= 8 and time_filters.get('asian_session_reduce', False):
                # Reduce but don't completely stop
                return np.random.random() > 0.3  # 70% chance to skip
            
            # Early Friday close
            if weekday == 4 and hour >= 15 and time_filters.get('friday_close_early', False):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking trading time: {e}")
            return True  # Default to allow trading
    
    def _generate_base_signal(self, market_state: MarketState, adaptive_params: Dict) -> Optional[TradingSignal]:
        """Generate base trading signal"""
        try:
            # Get signal confirmation score
            signal_score = market_state.signal_confirmation.get('total_score', 0)
            signal_threshold = adaptive_params['signal_threshold']
            
            if signal_score < signal_threshold:
                logger.debug(f"Signal score {signal_score} below threshold {signal_threshold}")
                return None
            
            # Determine signal direction
            bullish_signals = market_state.signal_confirmation.get('bullish_signals', [])
            bearish_signals = market_state.signal_confirmation.get('bearish_signals', [])
            
            if len(bullish_signals) > len(bearish_signals):
                action = 'buy'
                strength = signal_score / 100.0
            elif len(bearish_signals) > len(bullish_signals):
                action = 'sell'
                strength = signal_score / 100.0
            else:
                logger.debug("No clear signal direction")
                return None
            
            # Calculate entry price
            entry_price = market_state.ask if action == 'buy' else market_state.bid
            
            # Calculate stop loss and take profit using ATR
            atr = market_state.atr_data.atr
            sl_multiplier = adaptive_params['stop_loss_multiplier']
            tp_multiplier = adaptive_params['take_profit_multiplier']
            
            if action == 'buy':
                stop_loss = entry_price - (atr * sl_multiplier * 2.0)
                take_profit = entry_price + (atr * tp_multiplier * 3.0)
            else:
                stop_loss = entry_price + (atr * sl_multiplier * 2.0)
                take_profit = entry_price - (atr * tp_multiplier * 3.0)
            
            # Calculate base volume
            base_volume = getattr(self.config.trading, 'default_volume', 0.1)
            volume = base_volume * adaptive_params['position_size_multiplier']
            
            # Create signal
            signal = TradingSignal(
                action=action,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                volume=volume,
                strength=strength,
                reasoning=f"Adaptive signal - {market_state.market_regime.market_phase}, Score: {signal_score}"
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating base signal: {e}")
            return None
    
    def _apply_adaptive_adjustments(self, signal: TradingSignal, market_state: MarketState, 
                                  adaptive_params: Dict, account_info: Dict) -> TradingSignal:
        """Apply adaptive adjustments to the signal"""
        try:
            # Adjust volume based on account balance and risk
            adjusted_volume = self.risk_manager.calculate_position_size(
                symbol=self.config.mt5.symbol,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                account_balance=account_info['balance']
            )
            
            # Apply adaptive multiplier
            final_volume = adjusted_volume * adaptive_params['position_size_multiplier']
            
            # Ensure volume is within limits
            min_volume = getattr(self.config.trading, 'min_volume', 0.01)
            max_volume = getattr(self.config.trading, 'max_volume', 1.0)
            final_volume = max(min_volume, min(max_volume, final_volume))
            
            # Create adjusted signal
            adjusted_signal = TradingSignal(
                action=signal.action,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                volume=final_volume,
                strength=signal.strength,
                reasoning=f"{signal.reasoning} | Adaptive volume: {final_volume:.3f}"
            )
            
            return adjusted_signal
            
        except Exception as e:
            logger.error(f"Error applying adaptive adjustments: {e}")
            return signal
    
    def _validate_adaptive_signal(self, signal: TradingSignal, current_positions: List, 
                                market_state: MarketState) -> bool:
        """Validate adaptive signal before execution"""
        try:
            # Check maximum positions
            max_positions = getattr(self.config.risk, 'max_positions', 3)
            if len(current_positions) >= max_positions:
                logger.debug(f"Maximum positions ({max_positions}) reached")
                return False
            
            # Check if we already have a position in the same direction
            same_direction_positions = [
                pos for pos in current_positions 
                if (signal.action == 'buy' and pos.type == 0) or 
                   (signal.action == 'sell' and pos.type == 1)
            ]
            
            if len(same_direction_positions) >= 2:  # Max 2 positions in same direction
                logger.debug("Too many positions in same direction")
                return False
            
            # Check minimum signal strength
            min_strength = 0.5
            if signal.strength < min_strength:
                logger.debug(f"Signal strength {signal.strength} below minimum {min_strength}")
                return False
            
            # Check spread
            max_spread = getattr(self.config.trading, 'max_spread', 5.0)
            if market_state.spread > max_spread:
                logger.debug(f"Spread {market_state.spread} too high")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating adaptive signal: {e}")
            return False
    
    def should_close_position_adaptive(self, position, market_state: MarketState) -> Tuple[bool, str]:
        """Check if position should be closed based on adaptive criteria"""
        try:
            # Check basic risk management first
            should_close, reason = self.risk_manager.should_close_position(
                position, market_state, {}
            )
            
            if should_close:
                return True, f"Risk management: {reason}"
            
            # Adaptive closing criteria
            
            # 1. Market regime change
            current_regime = market_state.market_regime.market_phase
            
            # If we're in a long position but market turned bearish
            if position.type == 0 and 'bearish' in current_regime.lower():
                return True, "Market regime changed to bearish"
            
            # If we're in a short position but market turned bullish
            if position.type == 1 and 'bullish' in current_regime.lower():
                return True, "Market regime changed to bullish"
            
            # 2. Signal reversal
            signal_score = market_state.signal_confirmation.get('total_score', 0)
            bullish_signals = market_state.signal_confirmation.get('bullish_signals', [])
            bearish_signals = market_state.signal_confirmation.get('bearish_signals', [])
            
            # Close long position if strong bearish signals
            if position.type == 0 and len(bearish_signals) > len(bullish_signals) and signal_score > 60:
                return True, "Strong bearish signal reversal"
            
            # Close short position if strong bullish signals
            if position.type == 1 and len(bullish_signals) > len(bearish_signals) and signal_score > 60:
                return True, "Strong bullish signal reversal"
            
            # 3. Time-based exit (if position is very old)
            position_age_hours = 24  # Example: close after 24 hours
            # This would need actual position opening time to calculate
            
            return False, ""
            
        except Exception as e:
            logger.error(f"Error checking adaptive position close: {e}")
            return False, ""

@echo off
echo Starting MT5 Python Trading Bot Scheduler...
echo.
echo This will run the bot according to the following schedule:
echo - Monday 00:00 to Tuesday 23:59
echo - Friday 00:00 to Friday 23:59
echo.
echo Press Ctrl+C to stop the scheduler
echo.

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo Virtual environment activated.
) else (
    echo Virtual environment not found. Using system Python.
)

REM Run the scheduler
python scheduler.py

pause
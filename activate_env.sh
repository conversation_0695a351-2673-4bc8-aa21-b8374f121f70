#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
echo "✅ Virtual environment activated!"
echo "📍 Project: $(pwd)"
echo "🐍 Python: $(which python)"
echo "📦 Pip: $(which pip)"
echo ""
echo "🚨 IMPORTANT: This is a MOCK MT5 client on macOS"
echo "   No real trading will occur - for development/testing only"
echo ""
echo "🚀 Available commands:"
echo "   python main.py          # Run bot (mock mode)"
echo "   python run_tests.py     # Run tests"
echo "   python run_backtest.py  # Run backtest"
echo "   python quick_start.py   # Validate setup"
echo ""
echo "💡 To deactivate: deactivate"
exec "$SHELL"

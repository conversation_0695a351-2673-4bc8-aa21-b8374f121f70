#!/usr/bin/env python3
"""
Scheduler script for MT5 Python Trading Bot
Runs main.py according to the following schedule:
- Monday 00:00 to Tuesday 23:59
- Thursday 00:00 to Thursday 23:59
"""

import schedule
import time
import datetime
import subprocess
import sys
import os
from pathlib import Path

def is_trading_time():
    """
    Check if current time is within trading hours (UTC time)
    Returns True if it's Monday 00:00 to Tuesday 23:59 or Thursday 00:00 to Thursday 23:59
    """
    now = datetime.datetime.now(datetime.timezone.utc)  # Use UTC time
    weekday = now.weekday()  # Monday = 0, Tuesday = 1, ..., Sunday = 6

    # Monday (0) to Tuesday (1): 00:00 Monday to 23:59 Tuesday
    if weekday == 0:  # Monday
        return True
    elif weekday == 1:  # Tuesday
        return True
    elif weekday == 3:  # Thursday (Thứ 5)
        return True
    else:
        return False

def run_main_py():
    """
    Run main.py if it's within trading hours
    """
    if is_trading_time():
        print(f"[{datetime.datetime.now(datetime.timezone.utc)}] Starting main.py - Trading hours active (UTC)")
        try:
            # Run main.py
            result = subprocess.run([sys.executable, "main.py"],
                                  capture_output=True,
                                  text=True,
                                  cwd=Path(__file__).parent)

            if result.returncode == 0:
                print(f"[{datetime.datetime.now(datetime.timezone.utc)}] main.py completed successfully (UTC)")
            else:
                print(f"[{datetime.datetime.now(datetime.timezone.utc)}] main.py failed with return code {result.returncode} (UTC)")
                print(f"Error: {result.stderr}")

        except Exception as e:
            print(f"[{datetime.datetime.now(datetime.timezone.utc)}] Error running main.py: {e} (UTC)")
    else:
        print(f"[{datetime.datetime.now(datetime.timezone.utc)}] Skipping main.py - Outside trading hours (UTC)")

def print_schedule_info():
    """
    Print the current schedule information
    """
    print("=" * 60)
    print("MT5 Python Trading Bot Scheduler")
    print("=" * 60)
    print("Schedule (UTC time):")
    print("- Monday 00:00 to Tuesday 23:59")
    print("- Thursday 00:00 to Thursday 23:59")
    print("- Other days: No trading")
    print("=" * 60)
    print(f"Current UTC time: {datetime.datetime.now(datetime.timezone.utc)}")
    print(f"Trading hours active: {is_trading_time()}")
    print("=" * 60)

def main():
    """
    Main scheduler function
    """
    print_schedule_info()

    # Schedule the job to run every minute
    schedule.every().minute.do(run_main_py)

    print("Scheduler started. Press Ctrl+C to stop.")
    print("The bot will run every minute during trading hours.")

    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Wait 1 minute before next check

    except KeyboardInterrupt:
        print("\nScheduler stopped by user.")
        sys.exit(0)

if __name__ == "__main__":
    main()
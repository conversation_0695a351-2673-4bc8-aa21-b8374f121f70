# MT5 Python Trading Bot Scheduler

Script này cho phép chạy `main.py` theo lịch trình tự động.

## Lịch trình hoạt động (UTC)

- **Thứ 2**: 00:00 đến 23:59 UTC
- **Thứ 3**: 00:00 đến 23:59 UTC
- **Thứ 5**: 00:00 đến 23:59 UTC
- **<PERSON><PERSON><PERSON>y kh<PERSON>c**: Không chạy

**Lưu ý**: Script sử dụng thời gian UTC để đảm bảo tính nhất quán trên các múi giờ khác nhau.

## Cài đặt

1. Cài đặt thư viện `schedule`:
```bash
pip install schedule
```

Hoặc cài đặt tất cả dependencies:
```bash
pip install -r requirements.txt
```

## Cách sử dụng

### Trên Windows

Chạy file batch:
```cmd
run_scheduler.bat
```

Hoặc chạy trực tiếp Python:
```cmd
python scheduler.py
```

### Trên Linux/Mac

Chạy script shell:
```bash
./run_scheduler.sh
```

Hoặc chạy trực tiếp Python:
```bash
python scheduler.py
```

## Tính năng

- **Tự động kiểm tra thời gian**: Script sẽ kiểm tra mỗi phút xem có phải thời gian giao dịch không
- **Logging**: Hiển thị thông tin về thời gian chạy và kết quả
- **Error handling**: Xử lý lỗi khi chạy main.py
- **Dễ dừng**: Nhấn Ctrl+C để dừng scheduler

## Output mẫu

```
============================================================
MT5 Python Trading Bot Scheduler
============================================================
Schedule (UTC time):
- Monday 00:00 to Tuesday 23:59
- Thursday 00:00 to Thursday 23:59
- Other days: No trading
============================================================
Current UTC time: 2024-01-15 14:30:00
Trading hours active: False
============================================================
Scheduler started. Press Ctrl+C to stop.
The bot will run every minute during trading hours.
[2024-01-15 14:30:00] Skipping main.py - Outside trading hours (UTC)
[2024-01-15 14:31:00] Skipping main.py - Outside trading hours (UTC)
...
```

## Lưu ý

- Script sẽ chạy liên tục và kiểm tra mỗi phút
- Đảm bảo rằng `main.py` có thể chạy độc lập
- Nếu có lỗi trong `main.py`, scheduler sẽ tiếp tục chạy và thử lại ở lần tiếp theo
- Có thể chạy như một service hoặc background process để hoạt động 24/7

## Tùy chỉnh

Để thay đổi lịch trình, chỉnh sửa hàm `is_trading_time()` trong file `scheduler.py`:

```python
def is_trading_time():
    now = datetime.datetime.now()
    weekday = now.weekday()  # Monday = 0, Tuesday = 1, ..., Sunday = 6

    # Thêm điều kiện tùy chỉnh ở đây
    if weekday in [0, 1, 4]:  # Monday, Tuesday, Friday
        return True
    return False
```
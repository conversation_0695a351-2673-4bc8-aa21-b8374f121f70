#!/usr/bin/env python3
"""
Test Strategy Optimization Framework
Test the optimization framework with simulated results
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.optimization.strategy_optimizer import (
    StrategyOptimizer, OptimizationParameter, OptimizationResult
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_optimization_parameter():
    """Test OptimizationParameter functionality"""
    print("🧪 Testing OptimizationParameter")
    print("-" * 40)
    
    try:
        # Create test parameter
        param = OptimizationParameter(
            name="test_param",
            min_value=10.0,
            max_value=20.0,
            step=2.0
        )
        
        print(f"✅ Created parameter: {param.name}")
        print(f"   Range: {param.min_value} - {param.max_value}")
        print(f"   Step: {param.step}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing OptimizationParameter: {e}")
        return False


def test_optimization_result():
    """Test OptimizationResult functionality"""
    print("\n🧪 Testing OptimizationResult")
    print("-" * 40)
    
    try:
        # Create test result
        result = OptimizationResult(
            parameters={'macd_fast': 12, 'macd_slow': 26},
            total_return_pct=15.5,
            win_rate=0.65,
            profit_factor=1.8,
            max_drawdown_pct=0.12,
            sharpe_ratio=1.4,
            total_trades=45,
            score=0.75,
            market_condition='trending_bull',
            test_period='2024-01-01 to 2024-03-31'
        )
        
        print(f"✅ Created optimization result")
        print(f"   Score: {result.score:.3f}")
        print(f"   Return: {result.total_return_pct:.1f}%")
        print(f"   Win Rate: {result.win_rate:.1%}")
        print(f"   Market Condition: {result.market_condition}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing OptimizationResult: {e}")
        return False


def test_parameter_generation():
    """Test parameter combination generation"""
    print("\n🧪 Testing Parameter Generation")
    print("-" * 42)
    
    try:
        # Create simplified optimizer
        optimizer = StrategyOptimizer()
        
        # Simplify parameters for testing
        optimizer.optimization_params = {
            'macd_fast': OptimizationParameter('macd_fast', 10, 14, 2),
            'macd_slow': OptimizationParameter('macd_slow', 24, 28, 2),
            'signal_threshold': OptimizationParameter('signal_threshold', 0.6, 0.8, 0.1)
        }
        
        # Generate combinations
        combinations = optimizer._generate_parameter_combinations(max_combinations=20)
        
        print(f"✅ Generated {len(combinations)} parameter combinations")
        print(f"   Sample combination: {combinations[0] if combinations else 'None'}")
        
        # Test parameter ranges
        if combinations:
            macd_fast_values = [c['macd_fast'] for c in combinations]
            print(f"   MACD Fast range: {min(macd_fast_values)} - {max(macd_fast_values)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing parameter generation: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_optimization_scoring():
    """Test optimization scoring system"""
    print("\n🧪 Testing Optimization Scoring")
    print("-" * 42)
    
    try:
        optimizer = StrategyOptimizer()
        
        # Create mock backtest results
        class MockResults:
            def __init__(self, return_pct, win_rate, profit_factor, sharpe, drawdown_pct, trades):
                self.total_return_pct = return_pct
                self.win_rate = win_rate
                self.profit_factor = profit_factor
                self.sharpe_ratio = sharpe
                self.max_drawdown_pct = drawdown_pct
                self.total_trades = trades
        
        # Test different scenarios
        test_scenarios = [
            ("Excellent Strategy", MockResults(25, 0.7, 2.5, 2.0, 0.08, 50)),
            ("Good Strategy", MockResults(15, 0.6, 1.8, 1.5, 0.12, 35)),
            ("Average Strategy", MockResults(8, 0.5, 1.3, 1.0, 0.15, 25)),
            ("Poor Strategy", MockResults(-5, 0.3, 0.8, -0.5, 0.25, 20))
        ]
        
        print("📊 Testing scoring scenarios:")
        for name, results in test_scenarios:
            score = optimizer._calculate_optimization_score(results)
            print(f"   {name}: Score = {score:.3f}")
            print(f"     Return: {results.total_return_pct:.1f}%, "
                  f"Win Rate: {results.win_rate:.1%}, "
                  f"Drawdown: {results.max_drawdown_pct:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing optimization scoring: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_analysis_functions():
    """Test analysis and recommendation functions"""
    print("\n🧪 Testing Analysis Functions")
    print("-" * 40)
    
    try:
        optimizer = StrategyOptimizer()
        
        # Create mock optimization results
        np.random.seed(42)
        mock_results = []
        
        market_conditions = ['trending_bull', 'trending_bear', 'ranging', 'volatile']
        
        for i in range(20):
            condition = np.random.choice(market_conditions)
            
            # Generate realistic but varied results
            base_return = np.random.normal(10, 15)
            win_rate = np.random.uniform(0.4, 0.7)
            profit_factor = np.random.uniform(0.8, 2.5)
            sharpe = np.random.normal(1.0, 0.8)
            drawdown = np.random.uniform(0.05, 0.25)
            
            # Calculate score
            score = max(0, min(1, 
                (base_return/50 + win_rate + (profit_factor-1)/2 + sharpe/3 + (1-drawdown/0.3))/5
            ))
            
            result = OptimizationResult(
                parameters={
                    'macd_fast': np.random.choice([10, 12, 14, 16]),
                    'macd_slow': np.random.choice([24, 26, 28, 30]),
                    'signal_threshold': np.random.uniform(0.5, 0.8),
                    'risk_per_trade': np.random.uniform(0.01, 0.03)
                },
                total_return_pct=base_return,
                win_rate=win_rate,
                profit_factor=profit_factor,
                max_drawdown_pct=drawdown,
                sharpe_ratio=sharpe,
                total_trades=np.random.randint(15, 60),
                score=score,
                market_condition=condition,
                test_period=f"2024-{i%4+1:02d}-01 to 2024-{i%4+1:02d}-28"
            )
            
            mock_results.append(result)
        
        optimizer.optimization_results = mock_results
        
        print(f"✅ Created {len(mock_results)} mock optimization results")
        
        # Test analysis
        summary = optimizer._analyze_optimization_results()
        
        print(f"📊 Analysis Results:")
        print(f"   Best Overall Score: {summary['optimization_summary']['best_overall_score']:.3f}")
        print(f"   Mean Score: {summary['optimization_summary']['score_statistics']['mean']:.3f}")
        print(f"   Market Conditions Analyzed: {len(summary['market_condition_analysis'])}")
        print(f"   Parameters Analyzed: {len(summary['parameter_sensitivity'])}")
        print(f"   Recommendations: {len(summary['recommendations'])}")
        
        # Test recommendations
        recommendations = optimizer._generate_recommendations()
        print(f"\n💡 Sample Recommendations:")
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"   {i}. {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing analysis functions: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_sensitivity():
    """Test parameter sensitivity analysis"""
    print("\n🧪 Testing Parameter Sensitivity Analysis")
    print("-" * 48)
    
    try:
        optimizer = StrategyOptimizer()
        
        # Create mock results with known parameter relationships
        mock_results = []
        
        for i in range(30):
            macd_fast = 10 + (i % 6) * 2  # 10, 12, 14, 16, 18, 20
            signal_threshold = 0.5 + (i % 7) * 0.05  # 0.5 to 0.8
            
            # Create artificial correlation: higher macd_fast = better performance
            base_score = 0.3 + (macd_fast - 10) / 20  # 0.3 to 0.8
            # Add some noise
            score = base_score + np.random.normal(0, 0.1)
            score = max(0, min(1, score))
            
            result = OptimizationResult(
                parameters={
                    'macd_fast': macd_fast,
                    'macd_slow': 26,
                    'signal_threshold': signal_threshold,
                    'risk_per_trade': 0.02
                },
                total_return_pct=score * 30,
                win_rate=0.4 + score * 0.3,
                profit_factor=1.0 + score,
                max_drawdown_pct=0.2 - score * 0.1,
                sharpe_ratio=score * 2,
                total_trades=25,
                score=score,
                market_condition='trending_bull',
                test_period='2024-01-01 to 2024-03-31'
            )
            
            mock_results.append(result)
        
        optimizer.optimization_results = mock_results
        
        # Test sensitivity analysis
        sensitivity = optimizer._analyze_parameter_sensitivity()
        
        print(f"✅ Parameter Sensitivity Analysis:")
        for param, analysis in sensitivity.items():
            correlation = analysis['correlation_with_score']
            optimal_range = analysis['optimal_range']
            
            print(f"   {param}:")
            print(f"     Correlation: {correlation:+.3f}")
            print(f"     Optimal Range: {optimal_range['min']:.3f} - {optimal_range['max']:.3f}")
            print(f"     Optimal Mean: {optimal_range['mean']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing parameter sensitivity: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_results_saving():
    """Test results saving functionality"""
    print("\n🧪 Testing Results Saving")
    print("-" * 35)
    
    try:
        optimizer = StrategyOptimizer()
        
        # Create minimal mock results
        mock_result = OptimizationResult(
            parameters={'macd_fast': 12, 'macd_slow': 26},
            total_return_pct=15.0,
            win_rate=0.6,
            profit_factor=1.5,
            max_drawdown_pct=0.1,
            sharpe_ratio=1.2,
            total_trades=30,
            score=0.7,
            market_condition='trending_bull',
            test_period='2024-01-01 to 2024-03-31'
        )
        
        optimizer.optimization_results = [mock_result]
        
        # Test analysis and saving
        summary = optimizer._analyze_optimization_results()
        optimizer._save_optimization_results(summary)
        
        print(f"✅ Results saving tested")
        print(f"   Summary generated with {len(summary)} sections")
        print(f"   Files should be saved to reports/optimization/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing results saving: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all optimization framework tests"""
    print("🚀 Testing Strategy Optimization Framework")
    print("=" * 60)
    
    tests = [
        ("OptimizationParameter", test_optimization_parameter),
        ("OptimizationResult", test_optimization_result),
        ("Parameter Generation", test_parameter_generation),
        ("Optimization Scoring", test_optimization_scoring),
        ("Analysis Functions", test_analysis_functions),
        ("Parameter Sensitivity", test_parameter_sensitivity),
        ("Results Saving", test_results_saving)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All optimization framework features working correctly!")
        print("\n🎯 OPTIMIZATION FRAMEWORK CAPABILITIES:")
        print("   ✅ Parameter space exploration")
        print("   ✅ Multi-market condition testing")
        print("   ✅ Comprehensive scoring system")
        print("   ✅ Parameter sensitivity analysis")
        print("   ✅ Performance recommendations")
        print("   ✅ Results persistence and reporting")
        print("\n💡 The framework is ready for integration with live backtesting!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

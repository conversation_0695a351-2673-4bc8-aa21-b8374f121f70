#!/usr/bin/env python3
"""
Strategy Optimization and Testing Script
Comprehensive optimization and testing of the trading strategy
"""

import sys
import argparse
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.optimization.strategy_optimizer import StrategyOptimizer
from src.backtesting.backtest_engine import BacktestEngine, SimpleBacktestConfig
from src.utils.logger import get_logger

logger = get_logger(__name__)


def print_optimization_summary(summary):
    """Print comprehensive optimization summary"""
    print("\n" + "="*80)
    print("🎯 STRATEGY OPTIMIZATION RESULTS")
    print("="*80)
    
    opt_summary = summary['optimization_summary']
    
    # Overall Results
    print(f"\n📊 OVERALL OPTIMIZATION RESULTS:")
    print(f"  Total Combinations Tested: {opt_summary['total_combinations_tested']:,}")
    print(f"  Best Overall Score: {opt_summary['best_overall_score']:.3f}")
    print(f"  Best Overall Return: {opt_summary['best_overall_return']:.2f}%")
    
    # Score Statistics
    score_stats = opt_summary['score_statistics']
    print(f"\n📈 SCORE DISTRIBUTION:")
    print(f"  Mean Score: {score_stats['mean']:.3f}")
    print(f"  Score Range: {score_stats['min']:.3f} - {score_stats['max']:.3f}")
    print(f"  75th Percentile: {score_stats['percentile_75']:.3f}")
    print(f"  90th Percentile: {score_stats['percentile_90']:.3f}")
    
    # Return Statistics
    return_stats = opt_summary['return_statistics']
    print(f"\n💰 RETURN DISTRIBUTION:")
    print(f"  Mean Return: {return_stats['mean']:.2f}%")
    print(f"  Return Range: {return_stats['min']:.2f}% - {return_stats['max']:.2f}%")
    print(f"  Positive Returns: {return_stats['positive_returns_pct']:.1f}% of tests")
    
    # Best Overall Parameters
    print(f"\n🏆 BEST OVERALL PARAMETERS:")
    best_params = opt_summary['best_overall_parameters']
    for param, value in best_params.items():
        print(f"  {param}: {value}")
    
    # Market Condition Analysis
    print(f"\n🌍 MARKET CONDITION ANALYSIS:")
    market_analysis = summary['market_condition_analysis']
    for condition, analysis in market_analysis.items():
        print(f"\n  📊 {condition.upper().replace('_', ' ')}:")
        print(f"    Best Score: {analysis['best_score']:.3f}")
        print(f"    Best Return: {analysis['metrics']['total_return_pct']:.2f}%")
        print(f"    Win Rate: {analysis['metrics']['win_rate']:.1%}")
        print(f"    Profit Factor: {analysis['metrics']['profit_factor']:.2f}")
        print(f"    Max Drawdown: {analysis['metrics']['max_drawdown_pct']:.2%}")
        print(f"    Total Tests: {analysis['total_tests']}")
    
    # Parameter Sensitivity
    print(f"\n🎯 PARAMETER SENSITIVITY ANALYSIS:")
    param_sensitivity = summary['parameter_sensitivity']
    for param, sensitivity in param_sensitivity.items():
        correlation = sensitivity['correlation_with_score']
        optimal_range = sensitivity['optimal_range']
        print(f"  {param}:")
        print(f"    Correlation with Score: {correlation:+.3f}")
        print(f"    Optimal Range: {optimal_range['min']:.3f} - {optimal_range['max']:.3f}")
        print(f"    Optimal Mean: {optimal_range['mean']:.3f}")
    
    # Recommendations
    print(f"\n💡 OPTIMIZATION RECOMMENDATIONS:")
    for i, recommendation in enumerate(summary['recommendations'], 1):
        print(f"  {i}. {recommendation}")
    
    print("="*80)


def run_quick_optimization_test():
    """Run a quick optimization test with limited parameters"""
    print("🧪 Running Quick Optimization Test")
    print("-" * 50)
    
    try:
        # Initialize optimizer
        optimizer = StrategyOptimizer()
        
        # Reduce parameter ranges for quick test
        optimizer.optimization_params = {
            'macd_fast': optimizer.optimization_params['macd_fast'],
            'macd_slow': optimizer.optimization_params['macd_slow'],
            'signal_threshold': optimizer.optimization_params['signal_threshold'],
            'risk_per_trade': optimizer.optimization_params['risk_per_trade']
        }
        
        # Reduce test periods for quick test
        optimizer.test_periods = {
            'trending_bull': ('2024-01-01', '2024-02-29'),
            'ranging': ('2024-07-01', '2024-08-31')
        }
        
        print("✅ Optimizer configured for quick test")
        print(f"📊 Testing {len(optimizer.optimization_params)} parameters")
        print(f"🗓️ Testing {len(optimizer.test_periods)} market periods")
        
        # Run optimization with limited combinations
        results = optimizer.optimize_strategy(
            symbol="XAUUSD",
            max_combinations=50,  # Limited for quick test
            parallel_workers=1
        )
        
        print("\n✅ Quick optimization completed!")
        
        # Print summary
        if 'error' not in results:
            print_optimization_summary(results)
        else:
            print(f"❌ Optimization failed: {results['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick optimization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_comprehensive_optimization():
    """Run comprehensive optimization with full parameter space"""
    print("🚀 Running Comprehensive Strategy Optimization")
    print("-" * 60)
    
    try:
        # Initialize optimizer
        optimizer = StrategyOptimizer()
        
        print("✅ Optimizer initialized with full parameter space")
        print(f"📊 Testing {len(optimizer.optimization_params)} parameters")
        print(f"🗓️ Testing {len(optimizer.test_periods)} market periods")
        
        # Estimate total combinations
        total_combinations = 1
        for param_config in optimizer.optimization_params.values():
            param_range = (param_config.max_value - param_config.min_value) / param_config.step + 1
            total_combinations *= int(param_range)
        
        print(f"🔢 Estimated total combinations: {total_combinations:,}")
        
        # Run optimization
        results = optimizer.optimize_strategy(
            symbol="XAUUSD",
            max_combinations=2000,  # Reasonable limit for comprehensive test
            parallel_workers=1
        )
        
        print("\n✅ Comprehensive optimization completed!")
        
        # Print detailed summary
        if 'error' not in results:
            print_optimization_summary(results)
            
            # Save recommended parameters
            recommended_params = optimizer.get_recommended_parameters('overall')
            print(f"\n💾 Recommended parameters saved")
            print(f"📄 Detailed results saved to reports/optimization/")
            
        else:
            print(f"❌ Optimization failed: {results['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_validation_test(parameters: dict):
    """Run validation test with specific parameters"""
    print(f"🔍 Running Validation Test")
    print("-" * 40)
    
    try:
        from src.utils.config import load_config
        
        # Load base config
        config = load_config()
        
        # Apply test parameters
        if hasattr(config, 'indicators'):
            config.indicators.macd_fast = int(parameters.get('macd_fast', 12))
            config.indicators.macd_slow = int(parameters.get('macd_slow', 26))
            config.indicators.macd_signal = int(parameters.get('macd_signal', 9))
        
        # Create backtest engine
        engine = BacktestEngine(config)
        
        # Test on recent period
        simple_config = SimpleBacktestConfig.quick_setup("3months", 10000.0)
        
        print(f"📅 Testing period: {simple_config.start_date} to {simple_config.end_date}")
        print(f"💰 Initial balance: ${simple_config.initial_balance:,.2f}")
        
        # Run backtest
        results = engine.run_simple_backtest(simple_config, "XAUUSD")
        
        # Print validation results
        print(f"\n📊 VALIDATION RESULTS:")
        print(f"  Total Return: {results.total_return_pct:.2f}%")
        print(f"  Win Rate: {results.win_rate:.1%}")
        print(f"  Profit Factor: {results.profit_factor:.2f}")
        print(f"  Max Drawdown: {results.max_drawdown_pct:.2%}")
        print(f"  Sharpe Ratio: {results.sharpe_ratio:.2f}")
        print(f"  Total Trades: {results.total_trades}")
        
        # Performance rating
        if results.total_return_pct > 10 and results.max_drawdown_pct < 0.15:
            rating = "🌟 Excellent"
        elif results.total_return_pct > 5 and results.max_drawdown_pct < 0.20:
            rating = "⭐ Good"
        elif results.total_return_pct > 0:
            rating = "✅ Positive"
        else:
            rating = "❌ Negative"
        
        print(f"  Performance Rating: {rating}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main optimization function"""
    parser = argparse.ArgumentParser(description="Strategy Optimization and Testing")
    parser.add_argument("--mode", default="quick", 
                       choices=["quick", "comprehensive", "validate"],
                       help="Optimization mode")
    parser.add_argument("--symbol", default="XAUUSD", help="Trading symbol")
    parser.add_argument("--max-combinations", type=int, default=1000,
                       help="Maximum parameter combinations to test")
    
    args = parser.parse_args()
    
    print("🚀 Strategy Optimization and Testing")
    print("=" * 60)
    print(f"Mode: {args.mode.upper()}")
    print(f"Symbol: {args.symbol}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = False
    
    if args.mode == "quick":
        success = run_quick_optimization_test()
    elif args.mode == "comprehensive":
        success = run_comprehensive_optimization()
    elif args.mode == "validate":
        # Use default parameters for validation
        default_params = {
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'signal_threshold': 0.6,
            'risk_per_trade': 0.02
        }
        success = run_validation_test(default_params)
    
    if success:
        print("\n✅ Optimization and testing completed successfully!")
        print("📄 Check the reports/optimization/ directory for detailed results")
    else:
        print("\n❌ Optimization and testing failed")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# MT5 Python Trading Bot - Gold (XAU/USD) Specialist

An advanced automated trading bot for MetaTrader 5 (MT5) specializing in gold (XAU/USD) trading using AI/ML techniques, technical indicators, and reinforcement learning.

## 🎯 Features

- **Specialized Gold Trading**: Optimized for XAU/USD pair with 5-minute timeframe focus
- **Technical Indicators**: MACD, ATR, and Pivot Points for market analysis
- **AI/ML Integration**: Reinforcement learning models for intelligent trading decisions
- **Risk Management**: Advanced position sizing, stop-loss, and take-profit mechanisms
- **Real-time Trading**: Live market data processing and automated trade execution
- **Backtesting**: Comprehensive historical testing with performance analytics
- **Trading Time Analysis**: Advanced time-based pattern analysis and optimization
- **Volume Analysis**: Comprehensive volume and position sizing analysis
- **Cross-platform**: Works on Windows (live trading) and macOS/Linux (development/testing)

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MetaTrader 5 terminal (Windows only, for live trading)
- MT5 account with API access enabled

### Installation

1. **Clone and Setup:**
```bash
git clone https://github.com/your-username/mt5-python-trading-bot.git
cd mt5-python-trading-bot

# Automatic setup (recommended)
python setup_venv.py
```

2. **Configure Bot:**
```bash
cp config/config.example.yaml config/config.yaml
# Edit config/config.yaml with your MT5 credentials
```

3. **Quick Test:**
```bash
python quick_start.py
```

4. **Run Bot:**
```bash
python main.py          # Live trading
python run_backtest.py  # Backtesting
```

## 📊 Technical Indicators

- **MACD**: Trend-following momentum indicator
- **ATR**: Volatility indicator for position sizing
- **Pivot Points**: Support and resistance levels

## 🤖 AI/ML Components

- **Deep Q-Network (DQN)**: Reinforcement learning for decision making
- **Feature Engineering**: Technical indicator combinations
- **Continuous Learning**: Model adapts to changing market conditions

## ⚠️ Risk Management

- Dynamic position sizing based on ATR
- Stop-loss and take-profit automation
- Maximum drawdown protection
- Portfolio risk monitoring

## 🧪 Testing

```bash
python run_tests.py
```

## 📚 Documentation

- [Setup Guide](docs/SETUP.md) - Installation and configuration
- [API Reference](docs/API.md) - Code documentation
- [Strategy Guide](docs/STRATEGY.md) - Trading strategy details
- [Time Analysis Guide](docs/TIME_ANALYSIS.md) - Trading time pattern analysis
- [Volume Analysis Guide](docs/VOLUME_ANALYSIS.md) - Volume and position sizing analysis

## ⚠️ Disclaimer

This trading bot is for educational and research purposes. Trading involves significant risk of loss. Use at your own risk and never trade with money you cannot afford to lose.
# Trading Time Analysis

## Tổng quan

Tính năng phân tích thời gian giao dịch giúp bạn hiểu rõ các mẫu giao dịch theo thời gian và tối ưu hóa chiến lược giao dịch dựa trên thời gian hiệu quả nhất.

## Các biểu đồ được tạo

### 1. Trading Time Analysis (`trading_time_analysis.png`)
Biểu đồ tổng hợp gồm 4 phần:

- **Trading Activity by Hour of Day**: Hiển thị số lượng giao dịch theo từng giờ trong ngày
- **Trading Activity by Day of Week**: Hiển thị số lượng giao dịch theo từng ngày trong tuần
- **Trading Activity by Month**: Hiển thị số lượng giao dịch theo từng tháng
- **Daily Trading Activity Timeline**: Biểu đồ đường thể hiện số giao dịch mỗi ngày theo thời gian

### 2. Hourly P&L Analysis (`hourly_pnl_analysis.png`)
Phân tích P&L theo giờ:

- **Total P&L by Hour of Day**: Tổng P&L theo từng giờ
- **Average P&L by Hour of Day**: P&L trung bình theo từng giờ

### 3. Daily P&L Analysis (`daily_pnl_analysis.png`)
Phân tích P&L theo ngày:

- **Total P&L by Day of Week**: Tổng P&L theo từng ngày trong tuần
- **Average P&L by Day of Week**: P&L trung bình theo từng ngày trong tuần

## Thống kê tóm tắt trong HTML Report

HTML report sẽ hiển thị:

- **Best Hour**: Giờ có P&L cao nhất
- **Worst Hour**: Giờ có P&L thấp nhất
- **Best Day**: Ngày có P&L cao nhất
- **Worst Day**: Ngày có P&L thấp nhất
- **Most Active Hour**: Giờ có nhiều giao dịch nhất
- **Most Active Day**: Ngày có nhiều giao dịch nhất

## Cách sử dụng

### 1. Chạy backtest bình thường
```python
from src.backtesting.backtest_engine import BacktestEngine, BacktestReporter
from src.utils.config import BotConfig

# Load config
config = BotConfig.from_file('config/config.yaml')

# Create engine
engine = BacktestEngine(config)

# Run backtest
results = engine.run_backtest(data)

# Create reporter
reporter = BacktestReporter()

# Generate visualizations (bao gồm time analysis)
reporter.create_visualizations(results, save_dir="reports")

# Generate HTML report
reporter.generate_report(results, save_path="reports/backtest_report.html")
```

### 2. Chạy test riêng
```bash
python test_time_analysis.py
```

## Ý nghĩa của phân tích

### 1. Tối ưu hóa thời gian giao dịch
- Xác định các khung giờ có hiệu suất cao nhất
- Tránh giao dịch trong các khung giờ có hiệu suất thấp
- Điều chỉnh chiến lược theo mùa vụ

### 2. Quản lý rủi ro
- Hiểu rõ các mẫu thời gian có rủi ro cao
- Điều chỉnh position size theo thời gian
- Tối ưu hóa stop loss và take profit

### 3. Cải thiện chiến lược
- Thêm bộ lọc thời gian vào chiến lược
- Tối ưu hóa tham số theo khung giờ
- Phát triển chiến lược đa thời gian

## Ví dụ phân tích

### Kết quả mẫu:
```
Best Hour: Hour 14 ($1,250)
Worst Hour: Hour 22 (-$450)
Best Day: Wednesday ($2,100)
Worst Day: Friday (-$800)
Most Active Hour: Hour 10 (25 trades)
Most Active Day: Tuesday (45 trades)
```

### Diễn giải:
- **Giờ 14** là thời điểm tốt nhất để giao dịch (có thể do overlap giữa các thị trường)
- **Giờ 22** nên tránh giao dịch (có thể do thanh khoản thấp)
- **Thứ 4** là ngày hiệu quả nhất
- **Thứ 6** nên thận trọng (có thể do profit taking cuối tuần)

## Lưu ý

1. **Dữ liệu đủ**: Cần có đủ dữ liệu giao dịch để phân tích có ý nghĩa
2. **Thời gian thị trường**: Phân tích dựa trên thời gian thực tế của thị trường
3. **Tính ổn định**: Các mẫu có thể thay đổi theo thời gian, cần cập nhật định kỳ
4. **Kết hợp với phân tích khác**: Sử dụng cùng với các chỉ báo kỹ thuật khác

## Tùy chỉnh

Bạn có thể tùy chỉnh phân tích bằng cách:

1. **Thay đổi khung thời gian**: Sửa đổi trong `_plot_trading_time_analysis()`
2. **Thêm chỉ báo**: Bổ sung các chỉ báo khác như volatility, volume
3. **Tùy chỉnh màu sắc**: Thay đổi colormap trong matplotlib
4. **Thêm bộ lọc**: Lọc theo symbol, direction, hoặc các điều kiện khác
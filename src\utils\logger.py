"""
Logging configuration for MT5 Trading Bot
"""

import logging
import sys
import traceback
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any


def setup_logger(name: str = "mt5_bot", level: str = "INFO") -> logging.Logger:
    """
    Setup main logger for the application

    Args:
        name: Logger name
        level: Logging level

    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )

    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # File handler for detailed logs
    log_file = log_dir / f"mt5_bot_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)

    # Console handler for important messages
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(simple_formatter)

    # Error file handler
    error_file = log_dir / f"mt5_bot_errors_{datetime.now().strftime('%Y%m%d')}.log"
    error_handler = logging.FileHandler(error_file, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.addHandler(error_handler)

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get logger instance for a specific module

    Args:
        name: Module name

    Returns:
        Logger instance
    """
    return logging.getLogger(f"mt5_bot.{name}")


class TradingLogger:
    """Specialized logger for trading operations"""

    def __init__(self, name: str = "trading"):
        self.logger = get_logger(name)
        self.setup_trading_log()

    def setup_trading_log(self):
        """Setup specialized trading log file"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Comprehensive trading log (ALL levels)
        trading_file = log_dir / f"trading_{datetime.now().strftime('%Y%m%d')}.log"
        trading_handler = logging.FileHandler(trading_file, encoding='utf-8')
        trading_handler.setLevel(logging.DEBUG)  # Log everything

        trading_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        trading_handler.setFormatter(trading_formatter)

        # Signal analysis log (dedicated file for signals)
        signal_file = log_dir / f"signals_{datetime.now().strftime('%Y%m%d')}.log"
        signal_handler = logging.FileHandler(signal_file, encoding='utf-8')
        signal_handler.setLevel(logging.INFO)

        signal_formatter = logging.Formatter(
            '%(asctime)s - SIGNAL - %(message)s'
        )
        signal_handler.setFormatter(signal_formatter)

        # Warning and Error specific log
        warning_error_file = log_dir / f"trading_warnings_errors_{datetime.now().strftime('%Y%m%d')}.log"
        warning_error_handler = logging.FileHandler(warning_error_file, encoding='utf-8')
        warning_error_handler.setLevel(logging.WARNING)

        warning_error_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        warning_error_handler.setFormatter(warning_error_formatter)

        # Add handlers if not already present
        if not any(isinstance(h, logging.FileHandler) and 'trading_' in h.baseFilename and 'warnings_errors' not in h.baseFilename
                  for h in self.logger.handlers):
            self.logger.addHandler(trading_handler)

        if not any(isinstance(h, logging.FileHandler) and 'signals_' in h.baseFilename
                  for h in self.logger.handlers):
            self.logger.addHandler(signal_handler)

        if not any(isinstance(h, logging.FileHandler) and 'warnings_errors' in h.baseFilename
                  for h in self.logger.handlers):
            self.logger.addHandler(warning_error_handler)

    def log_trade_entry(self, symbol: str, action: str, volume: float,
                       price: float, sl: float = 0, tp: float = 0, **kwargs):
        """Log trade entry"""
        message = (f"ENTRY - {action.upper()} {volume} {symbol} @ {price:.5f} "
                  f"SL: {sl:.5f} TP: {tp:.5f}")

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

    def log_trade_exit(self, symbol: str, action: str, volume: float,
                      entry_price: float, exit_price: float, profit: float, **kwargs):
        """Log trade exit"""
        message = (f"EXIT - {action.upper()} {volume} {symbol} "
                  f"Entry: {entry_price:.5f} Exit: {exit_price:.5f} "
                  f"P&L: {profit:.2f}")

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

    def log_signal(self, signal_type: str, strength: float, indicators: dict, **kwargs):
        """Log trading signal"""
        message = f"SIGNAL - {signal_type.upper()} (Strength: {strength:.2f})"

        # Add indicator values
        if indicators:
            ind_str = " | ".join([f"{k}: {v}" for k, v in indicators.items()])
            message += f" | {ind_str}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

    def log_error(self, operation: str, error: str, **kwargs):
        """Log trading error with full traceback"""
        message = f"ERROR - {operation}: {error}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        # Add full traceback for errors
        traceback_info = traceback.format_exc()
        if traceback_info and traceback_info != "NoneType: None\n":
            message += f"\nTraceback:\n{traceback_info}"

        self.logger.error(message)

    def log_warning(self, operation: str, warning: str, **kwargs):
        """Log trading warning"""
        message = f"WARNING - {operation}: {warning}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.warning(message)

    def log_debug(self, operation: str, details: str, **kwargs):
        """Log debug information"""
        message = f"DEBUG - {operation}: {details}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.debug(message)

    def log_performance(self, period: str, metrics: dict):
        """Log performance metrics"""
        message = f"PERFORMANCE - {period}: "
        metrics_str = " | ".join([f"{k}: {v}" for k, v in metrics.items()])
        message += metrics_str

        self.logger.info(message)

    def log_risk_check(self, check_type: str, result: bool, details: str, **kwargs):
        """Log risk management checks"""
        status = "PASSED" if result else "FAILED"
        message = f"RISK_CHECK - {check_type}: {status} - {details}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        # Use different log levels based on check type and result
        if kwargs.get('repeat', False):
            level = logging.DEBUG  # Repeat violations only log DEBUG
        elif result:
            # PASSED checks - only log important ones
            if check_type in ["TRADING_ALLOWED"]:
                level = logging.INFO
            else:
                level = logging.DEBUG
        else:
            # FAILED checks - use different levels based on severity
            if check_type in ["MAX_DRAWDOWN", "MARGIN_LEVEL"]:
                level = logging.ERROR  # Critical failures
            elif check_type in ["TOTAL_RISK", "MAX_POSITIONS"]:
                level = logging.WARNING  # Important but not critical
            else:
                level = logging.DEBUG  # Minor checks

        self.logger.log(level, message)

    def log_market_data(self, data_type: str, data: Dict[str, Any], **kwargs):
        """Log market data updates"""
        message = f"MARKET_DATA - {data_type}: "

        # Format market data nicely
        data_str = " | ".join([f"{k}: {v}" for k, v in data.items()])
        message += data_str

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.debug(message)

    def log_config_change(self, config_section: str, old_value: Any, new_value: Any, **kwargs):
        """Log configuration changes"""
        message = f"CONFIG_CHANGE - {config_section}: {old_value} -> {new_value}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

    def log_connection_status(self, status: str, details: str, **kwargs):
        """Log connection status changes"""
        message = f"CONNECTION - {status}: {details}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        level = logging.INFO if status == "CONNECTED" else logging.WARNING
        self.logger.log(level, message)

    def log_order_status(self, order_id: str, status: str, details: str, **kwargs):
        """Log order status changes"""
        message = f"ORDER_STATUS - {order_id}: {status} - {details}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        # Use appropriate log level based on status
        if status in ["FILLED", "PARTIALLY_FILLED"]:
            level = logging.INFO
        elif status in ["REJECTED", "CANCELLED", "EXPIRED"]:
            level = logging.WARNING
        else:
            level = logging.DEBUG

        self.logger.log(level, message)

    def log_balance_update(self, old_balance: float, new_balance: float,
                          change: float, reason: str, **kwargs):
        """Log balance updates"""
        message = (f"BALANCE_UPDATE - {old_balance:.2f} -> {new_balance:.2f} "
                  f"(Change: {change:+.2f}) - {reason}")

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

    def log_strategy_event(self, event_type: str, details: str, **kwargs):
        """Log strategy-specific events"""
        message = f"STRATEGY_EVENT - {event_type}: {details}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

    def log_ai_prediction(self, prediction: int, confidence: float, features: Dict[str, Any], **kwargs):
        """Log AI model predictions"""
        prediction_map = {0: "HOLD", 1: "BUY", 2: "SELL"}
        prediction_str = prediction_map.get(prediction, f"UNKNOWN({prediction})")

        message = f"AI_PREDICTION - {prediction_str} (Confidence: {confidence:.3f})"

        # Add key features
        if features:
            feature_str = " | ".join([f"{k}: {v:.3f}" for k, v in features.items() if isinstance(v, (int, float))])
            if feature_str:
                message += f" | Features: {feature_str}"

        if kwargs:
            extras = " | ".join([f"{k}: {v}" for k, v in kwargs.items()])
            message += f" | {extras}"

        self.logger.info(message)

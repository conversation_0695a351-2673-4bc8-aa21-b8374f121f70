#!/usr/bin/env python3
"""
Test script to compare signal generation logic between backtest and real bot
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from src.utils.config import load_config
from src.backtesting.backtest_engine import BacktestEngine
from src.strategy.gold_strategy import GoldTradingStrategy, MarketState
from src.indicators.technical_indicators import MACDSignal, ATRData, PivotPoints

def create_test_market_state():
    """Create a test market state that should generate signals"""
    return MarketState(
        price=3342.76,
        bid=3342.68,
        ask=3342.84,
        spread=0.16,
        macd_signal=MACDSignal(
            macd=0.1,
            signal=0.05,
            histogram=0.05,
            trend='bullish',
            crossover='bullish_cross'
        ),
        atr_data=ATRData(
            atr=1.83,
            atr_percentage=0.055,
            volatility_level='low'
        ),
        pivot_points=PivotPoints(
            pivot=3340.0,
            r1=3345.0,
            r2=3350.0,
            r3=3355.0,
            s1=3335.0,
            s2=3330.0,
            s3=3325.0,
            current_level='above_pivot'
        ),
        ai_prediction=None,
        ai_confidence=0.0,
        market_trend='bullish',
        volatility='low'
    )

def test_backtest_signal_logic():
    """Test backtest signal generation logic"""
    print("=== Testing Backtest Signal Logic ===")

    config = load_config()
    backtest_engine = BacktestEngine(config)

    # Create test market state
    market_state = create_test_market_state()

    # Test signal generation
    signal = backtest_engine._evaluate_strategy_conditions(market_state)

    if signal:
        print(f"✅ Backtest generated signal: {signal.action.upper()}")
        print(f"   Strength: {signal.strength:.3f}")
        print(f"   Reasoning: {signal.reasoning}")
    else:
        print("❌ Backtest did not generate signal")

    return signal

def test_real_bot_signal_logic():
    """Test real bot signal generation logic"""
    print("\n=== Testing Real Bot Signal Logic ===")

    config = load_config()

    # Create a mock MT5Client class for testing
    class MockMT5Client:
        def get_historical_data(self, timeframe, count):
            return pd.DataFrame({
                'open': [3340] * 100,
                'high': [3345] * 100,
                'low': [3335] * 100,
                'close': [3342] * 100,
                'volume': [100] * 100
            })

        def get_current_price(self):
            return 3342.68, 3342.84

        def get_symbol_info(self):
            return {'spread': 16}

    strategy = GoldTradingStrategy(config, MockMT5Client())

    # Create test market state
    market_state = create_test_market_state()

    # Test signal generation
    signal = strategy._evaluate_trading_conditions(market_state, [])

    if signal:
        print(f"✅ Real bot generated signal: {signal.action.upper()}")
        print(f"   Strength: {signal.strength:.3f}")
        print(f"   Reasoning: {signal.reasoning}")
    else:
        print("❌ Real bot did not generate signal")

    return signal

def test_different_market_conditions():
    """Test different market conditions"""
    print("\n=== Testing Different Market Conditions ===")

    config = load_config()
    backtest_engine = BacktestEngine(config)

    # Create a mock MT5Client class for testing
    class MockMT5Client:
        def get_historical_data(self, timeframe, count):
            return pd.DataFrame({
                'open': [3340] * 100,
                'high': [3345] * 100,
                'low': [3335] * 100,
                'close': [3342] * 100,
                'volume': [100] * 100
            })

        def get_current_price(self):
            return 3342.68, 3342.84

        def get_symbol_info(self):
            return {'spread': 16}

    strategy = GoldTradingStrategy(config, MockMT5Client())

    # Test case 1: Strong bullish signal
    print("\n--- Test Case 1: Strong Bullish Signal ---")
    market_state = MarketState(
        price=3342.76,
        bid=3342.68,
        ask=3342.84,
        spread=0.16,
        macd_signal=MACDSignal(0.1, 0.05, 0.05, 'bullish', 'bullish_cross'),
        atr_data=ATRData(1.83, 0.055, 'low'),
        pivot_points=PivotPoints(3340.0, 3345.0, 3350.0, 3355.0, 3335.0, 3330.0, 3325.0, 'above_pivot'),
        ai_prediction=None, ai_confidence=0.0, market_trend='bullish', volatility='low'
    )

    backtest_signal = backtest_engine._evaluate_strategy_conditions(market_state)
    real_signal = strategy._evaluate_trading_conditions(market_state, [])

    print(f"Backtest signal: {'✅' if backtest_signal else '❌'} (strength: {backtest_signal.strength if backtest_signal else 0:.3f})")
    print(f"Real bot signal: {'✅' if real_signal else '❌'} (strength: {real_signal.strength if real_signal else 0:.3f})")

    # Test case 2: Weak signal
    print("\n--- Test Case 2: Weak Signal ---")
    market_state = MarketState(
        price=3342.76,
        bid=3342.68,
        ask=3342.84,
        spread=0.16,
        macd_signal=MACDSignal(0.01, 0.005, 0.005, 'bullish', 'none'),
        atr_data=ATRData(1.83, 0.055, 'low'),
        pivot_points=PivotPoints(3340.0, 3345.0, 3350.0, 3355.0, 3335.0, 3330.0, 3325.0, 'below_pivot'),
        ai_prediction=None, ai_confidence=0.0, market_trend='sideways', volatility='low'
    )

    backtest_signal = backtest_engine._evaluate_strategy_conditions(market_state)
    real_signal = strategy._evaluate_trading_conditions(market_state, [])

    print(f"Backtest signal: {'✅' if backtest_signal else '❌'} (strength: {backtest_signal.strength if backtest_signal else 0:.3f})")
    print(f"Real bot signal: {'✅' if real_signal else '❌'} (strength: {real_signal.strength if real_signal else 0:.3f})")

def test_strength_calculation():
    """Test strength calculation in detail"""
    print("\n=== Testing Strength Calculation ===")

    config = load_config()

    # Test the exact conditions from the log
    print("\n--- Testing Log Conditions (05:10) ---")
    market_state = MarketState(
        price=3342.76,
        bid=3342.68,
        ask=3342.84,
        spread=0.16,
        macd_signal=MACDSignal(0.0, 0.0, 0.0, 'bullish', 'none'),
        atr_data=ATRData(1.83, 0.055, 'low'),
        pivot_points=PivotPoints(3340.0, 3345.0, 3350.0, 3355.0, 3335.0, 3330.0, 3325.0, 'below_pivot'),
        ai_prediction=None, ai_confidence=0.0, market_trend='sideways', volatility='low'
    )

    # Calculate strength manually
    buy_score = 0
    sell_score = 0

    # MACD analysis
    if market_state.macd_signal.crossover == 'bullish_cross':
        buy_score += config.strategy_scoring.macd_crossover_bullish
    elif market_state.macd_signal.crossover == 'bearish_cross':
        sell_score += config.strategy_scoring.macd_crossover_bearish
    elif market_state.macd_signal.trend == 'bullish':
        buy_score += config.strategy_scoring.macd_trend_bullish
    elif market_state.macd_signal.trend == 'bearish':
        sell_score += config.strategy_scoring.macd_trend_bearish

    # Pivot Points analysis
    if market_state.pivot_points.current_level == 'above_pivot':
        buy_score += config.strategy_scoring.pivot_above
    elif market_state.pivot_points.current_level == 'below_pivot':
        sell_score += config.strategy_scoring.pivot_below

    # Market trend
    if market_state.market_trend == 'bullish':
        buy_score += config.strategy_scoring.market_trend_bullish
    elif market_state.market_trend == 'bearish':
        sell_score += config.strategy_scoring.market_trend_bearish

    # Apply technical weight
    buy_score *= config.strategy_scoring.technical_weight
    sell_score *= config.strategy_scoring.technical_weight

    print(f"MACD trend: {market_state.macd_signal.trend}")
    print(f"MACD crossover: {market_state.macd_signal.crossover}")
    print(f"Pivot level: {market_state.pivot_points.current_level}")
    print(f"Market trend: {market_state.market_trend}")
    print(f"Buy score: {buy_score:.3f}")
    print(f"Sell score: {sell_score:.3f}")
    print(f"Min required: {config.strategy_scoring.min_signal_strength}")
    print(f"Would generate signal: {'Yes' if max(buy_score, sell_score) > config.strategy_scoring.min_signal_strength else 'No'}")

def main():
    """Main test function"""
    print("Signal Generation Logic Comparison Test")
    print("=" * 50)

    # Test basic signal logic
    backtest_signal = test_backtest_signal_logic()
    real_signal = test_real_bot_signal_logic()

    # Test different conditions
    test_different_market_conditions()

    # Test strength calculation
    test_strength_calculation()

    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Backtest signal generated: {'Yes' if backtest_signal else 'No'}")
    print(f"Real bot signal generated: {'Yes' if real_signal else 'No'}")

    if backtest_signal and real_signal:
        print("✅ Both systems generate signals - Logic is aligned")
    elif not backtest_signal and not real_signal:
        print("✅ Both systems reject signals - Logic is aligned")
    else:
        print("❌ Systems behave differently - Logic mismatch detected")
        print("   This explains why backtest shows trades but real bot doesn't execute them")

if __name__ == "__main__":
    main()
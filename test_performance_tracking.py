#!/usr/bin/env python3
"""
Test Performance Tracking System
Quick test to verify the performance tracking and dashboard features
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.performance.performance_tracker import PerformanceTracker
from src.performance.dashboard import PerformanceDashboard
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_performance_tracker():
    """Test the performance tracker functionality"""
    print("🧪 Testing Performance Tracker")
    print("-" * 40)
    
    try:
        # Initialize tracker
        tracker = PerformanceTracker(initial_balance=10000.0)
        print(f"✅ Initialized tracker with ${tracker.initial_balance:,.2f}")
        
        # Test trade recording
        print("\n📊 Testing Trade Recording:")
        
        # Record some sample trades
        trades_data = [
            {
                'trade_id': 'T001',
                'symbol': 'XAUUSD',
                'direction': 'long',
                'entry_price': 2000.0,
                'volume': 0.1,
                'stop_loss': 1980.0,
                'take_profit': 2040.0,
                'market_condition': 'trending_bullish',
                'signal_confidence': 0.8,
                'exit_price': 2025.0,
                'commission': 3.0,
                'swap': 0.5,
                'exit_reason': 'tp'
            },
            {
                'trade_id': 'T002',
                'symbol': 'XAUUSD',
                'direction': 'short',
                'entry_price': 2025.0,
                'volume': 0.15,
                'stop_loss': 2045.0,
                'take_profit': 1995.0,
                'market_condition': 'ranging',
                'signal_confidence': 0.6,
                'exit_price': 2035.0,
                'commission': 4.5,
                'swap': -1.0,
                'exit_reason': 'sl'
            },
            {
                'trade_id': 'T003',
                'symbol': 'XAUUSD',
                'direction': 'long',
                'entry_price': 2010.0,
                'volume': 0.2,
                'stop_loss': 1990.0,
                'take_profit': 2050.0,
                'market_condition': 'trending_bullish',
                'signal_confidence': 0.9,
                'exit_price': 2045.0,
                'commission': 6.0,
                'swap': 1.5,
                'exit_reason': 'tp'
            }
        ]
        
        for trade_data in trades_data:
            # Record entry
            tracker.record_trade_entry(
                trade_data['trade_id'],
                trade_data['symbol'],
                trade_data['direction'],
                trade_data['entry_price'],
                trade_data['volume'],
                trade_data['stop_loss'],
                trade_data['take_profit'],
                trade_data['market_condition'],
                trade_data['signal_confidence']
            )
            
            # Small delay to simulate time passage
            time.sleep(0.1)
            
            # Record exit
            tracker.record_trade_exit(
                trade_data['trade_id'],
                trade_data['exit_price'],
                trade_data['commission'],
                trade_data['swap'],
                trade_data['exit_reason']
            )
            
            print(f"  ✅ Recorded trade {trade_data['trade_id']}")
        
        # Test metrics calculation
        print("\n📈 Testing Metrics Calculation:")
        metrics = tracker.get_current_metrics()
        
        print(f"  Total Trades: {metrics.total_trades}")
        print(f"  Win Rate: {metrics.win_rate:.1%}")
        print(f"  Total P&L: ${metrics.total_pnl:.2f}")
        print(f"  Profit Factor: {metrics.profit_factor:.2f}")
        print(f"  Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
        print(f"  Max Drawdown: {metrics.max_drawdown_pct:.2%}")
        print(f"  Current Balance: ${tracker.current_balance:.2f}")
        
        # Test performance summary
        print("\n📋 Testing Performance Summary:")
        summary = tracker.get_performance_summary()
        
        print(f"  Account Return: {summary['account_info']['total_return_pct']:.2f}%")
        print(f"  Risk Level: Based on {summary['risk_metrics']['max_drawdown_pct']:.1f}% max drawdown")
        print(f"  Market Conditions: {len(summary['market_conditions'])} analyzed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing performance tracker: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_dashboard():
    """Test the performance dashboard functionality"""
    print("\n🧪 Testing Performance Dashboard")
    print("-" * 42)
    
    try:
        # Initialize tracker with some data
        tracker = PerformanceTracker(initial_balance=5000.0)
        
        # Add some sample trades for dashboard testing
        sample_trades = [
            ('T101', 'long', 2000.0, 0.1, 2020.0, 'trending_bullish', 0.8),
            ('T102', 'short', 2020.0, 0.15, 2010.0, 'ranging', 0.6),
            ('T103', 'long', 2010.0, 0.12, 2035.0, 'trending_bullish', 0.9),
            ('T104', 'short', 2035.0, 0.08, 2045.0, 'ranging', 0.5),
            ('T105', 'long', 2015.0, 0.18, 2040.0, 'breakout', 0.85)
        ]
        
        for i, (trade_id, direction, entry, volume, exit_price, condition, confidence) in enumerate(sample_trades):
            tracker.record_trade_entry(
                trade_id, 'XAUUSD', direction, entry, volume,
                entry - 20 if direction == 'long' else entry + 20,  # stop loss
                entry + 30 if direction == 'long' else entry - 30,  # take profit
                condition, confidence
            )
            
            time.sleep(0.05)  # Small delay
            
            tracker.record_trade_exit(
                trade_id, exit_price, 2.0 + i, 0.5, 'tp'
            )
        
        # Create dashboard
        dashboard = PerformanceDashboard(tracker)
        
        print("✅ Created performance dashboard")
        
        # Generate dashboard data
        dashboard_data = dashboard.generate_dashboard_data()
        
        print("\n📊 Dashboard Sections Generated:")
        for section in dashboard_data.keys():
            if section != 'timestamp':
                print(f"  ✅ {section.replace('_', ' ').title()}")
        
        # Test specific dashboard sections
        print("\n🎯 Testing Dashboard Content:")
        
        account_overview = dashboard_data['account_overview']
        print(f"  Account Status: {account_overview['status']}")
        print(f"  Total Return: {account_overview['total_return_pct']}")
        
        performance_metrics = dashboard_data['performance_metrics']
        print(f"  Performance Rating: {performance_metrics['performance_rating']}")
        print(f"  Win Rate: {performance_metrics['win_rate']}")
        
        risk_analysis = dashboard_data['risk_analysis']
        print(f"  Risk Level: {risk_analysis['risk_level']}")
        print(f"  Sharpe Ratio: {risk_analysis['sharpe_ratio']}")
        
        # Test alerts
        alerts = dashboard_data['alerts']
        print(f"\n🚨 Performance Alerts: {len(alerts)} alerts generated")
        for alert in alerts:
            print(f"  {alert['type'].upper()}: {alert['title']}")
        
        # Test data export
        print("\n💾 Testing Data Export:")
        export_path = dashboard.export_dashboard_data()
        print(f"  ✅ Dashboard data exported to: {export_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing performance dashboard: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_real_time_monitoring():
    """Test real-time monitoring capabilities"""
    print("\n🧪 Testing Real-Time Monitoring")
    print("-" * 38)
    
    try:
        tracker = PerformanceTracker(initial_balance=8000.0)
        dashboard = PerformanceDashboard(tracker)
        
        print("📡 Simulating real-time trading activity...")
        
        # Simulate a trading session
        for i in range(5):
            trade_id = f"RT{i+1:03d}"
            
            # Random trade parameters
            import random
            direction = random.choice(['long', 'short'])
            entry_price = 2000 + random.uniform(-50, 50)
            volume = round(random.uniform(0.05, 0.25), 2)
            market_condition = random.choice(['trending_bullish', 'trending_bearish', 'ranging', 'breakout'])
            confidence = random.uniform(0.5, 0.95)
            
            # Record entry
            tracker.record_trade_entry(
                trade_id, 'XAUUSD', direction, entry_price, volume,
                entry_price - 25 if direction == 'long' else entry_price + 25,
                entry_price + 35 if direction == 'long' else entry_price - 35,
                market_condition, confidence
            )
            
            print(f"  📈 Trade {trade_id} opened: {direction} {volume} @ {entry_price:.2f}")
            
            # Simulate some time passing
            time.sleep(0.1)
            
            # Random exit
            if random.random() > 0.3:  # 70% chance to close
                exit_price = entry_price + random.uniform(-30, 40)
                tracker.record_trade_exit(trade_id, exit_price, 3.0, 0.5, 'manual')
                print(f"  📉 Trade {trade_id} closed @ {exit_price:.2f}")
            else:
                print(f"  ⏳ Trade {trade_id} still open")
        
        # Get real-time metrics
        current_metrics = tracker.get_current_metrics()
        print(f"\n📊 Real-Time Metrics:")
        print(f"  Current Balance: ${tracker.current_balance:.2f}")
        print(f"  Open Trades: {len(tracker.open_trades)}")
        print(f"  Total Trades: {current_metrics.total_trades}")
        print(f"  Win Rate: {current_metrics.win_rate:.1%}")
        print(f"  Current Drawdown: {current_metrics.current_drawdown_pct:.2%}")
        
        # Generate real-time dashboard
        dashboard_data = dashboard.generate_dashboard_data()
        print(f"\n🎛️  Real-Time Dashboard Updated: {dashboard_data['timestamp']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing real-time monitoring: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all performance tracking tests"""
    print("🚀 Testing Performance Tracking System")
    print("=" * 60)
    
    tests = [
        ("Performance Tracker", test_performance_tracker),
        ("Performance Dashboard", test_performance_dashboard),
        ("Real-Time Monitoring", test_real_time_monitoring)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All performance tracking features working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

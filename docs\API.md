# API Reference - MT5 Trading Bot

## Core Components

### MT5Client
Main interface for MetaTrader 5 operations.

```python
from src.core.mt5_client import MT5Client

client = MT5Client(login, password, server)
client.connect()
```

**Key Methods:**
- `connect() -> bool`: Connect to MT5 terminal
- `get_account_info() -> Dict`: Get account information
- `get_current_price() -> Tuple[float, float]`: Get bid/ask prices
- `get_historical_data(timeframe, count) -> pd.DataFrame`: Get historical data
- `buy_market(volume, sl, tp) -> Dict`: Place buy order
- `sell_market(volume, sl, tp) -> Dict`: Place sell order
- `get_positions() -> List[PositionInfo]`: Get open positions
- `close_position(ticket) -> Dict`: Close specific position

### TechnicalIndicators
Calculate technical indicators for market analysis.

```python
from src.indicators.technical_indicators import TechnicalIndicators

indicators = TechnicalIndicators()
macd = indicators.calculate_macd(data)
atr = indicators.calculate_atr(data)
pivots = indicators.calculate_pivot_points(data)
```

**Key Methods:**
- `calculate_macd(data) -> MACDSignal`: Calculate MACD indicator
- `calculate_atr(data) -> ATRData`: Calculate ATR indicator
- `calculate_pivot_points(data) -> PivotPoints`: Calculate pivot points
- `get_combined_signal(data) -> Dict`: Get combined analysis

### RiskManager
Manage trading risk and position sizing.

```python
from src.risk.risk_manager import RiskManager

risk_manager = RiskManager(risk_config, trading_config)
position_size = risk_manager.calculate_position_size(balance, entry, sl, atr, symbol_info)
```

**Key Methods:**
- `calculate_position_size(...) -> PositionSize`: Calculate optimal position size
- `check_trading_allowed(positions, account) -> Tuple[bool, str]`: Check if trading allowed
- `should_close_position(position, price, atr) -> Tuple[bool, str]`: Check if position should close

### GoldTradingStrategy
Main trading strategy for gold.

```python
from src.strategy.gold_strategy import GoldTradingStrategy

strategy = GoldTradingStrategy(config, mt5_client)
market_state = strategy.analyze_market()
signal = strategy.generate_signal(market_state, positions, account)
```

**Key Methods:**
- `analyze_market() -> MarketState`: Analyze current market conditions
- `generate_signal(market_state, positions, account) -> TradingSignal`: Generate trading signal

### BacktestEngine
Run comprehensive backtests.

```python
from src.backtesting.backtest_engine import BacktestEngine

engine = BacktestEngine(config)
results = engine.run_backtest(data, start_date, end_date)
```

**Key Methods:**
- `run_backtest(data, start_date, end_date) -> BacktestResults`: Run backtest

## Data Structures

### TradingSignal
```python
@dataclass
class TradingSignal:
    action: str          # 'buy', 'sell', 'hold', 'close'
    strength: float      # Signal strength 0-1
    entry_price: float   # Entry price
    stop_loss: float     # Stop loss price
    take_profit: float   # Take profit price
    volume: float        # Position size
    confidence: float    # AI confidence 0-1
    reasoning: str       # Human-readable reasoning
    timestamp: datetime  # Signal timestamp
```

### MarketState
```python
@dataclass
class MarketState:
    price: float              # Current price
    bid: float               # Bid price
    ask: float               # Ask price
    spread: float            # Spread
    macd_signal: MACDSignal  # MACD analysis
    atr_data: ATRData        # ATR analysis
    pivot_points: PivotPoints # Pivot points
    ai_prediction: int       # AI prediction (0=Hold, 1=Buy, 2=Sell)
    ai_confidence: float     # AI confidence
    market_trend: str        # 'bullish', 'bearish', 'sideways'
    volatility: str          # 'low', 'medium', 'high'
```

### PositionSize
```python
@dataclass
class PositionSize:
    volume: float           # Calculated volume
    risk_amount: float      # Risk amount in currency
    stop_loss: float        # Stop loss price
    take_profit: float      # Take profit price
    risk_reward_ratio: float # Risk/reward ratio
```

### BacktestResults
```python
@dataclass
class BacktestResults:
    start_date: datetime
    end_date: datetime
    initial_balance: float
    final_balance: float
    total_return: float
    total_return_pct: float
    max_drawdown: float
    max_drawdown_pct: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    trades: List[Trade]
    equity_curve: pd.Series
    drawdown_curve: pd.Series
```

## Configuration

### BotConfig Structure
```python
@dataclass
class BotConfig:
    mt5: MT5Config
    trading: TradingConfig
    risk: RiskConfig
    ai: AIConfig
    live_trading: bool
```

### MT5Config
```python
@dataclass
class MT5Config:
    login: int
    password: str
    server: str
    symbol: str
    magic_number: int
```

### TradingConfig
```python
@dataclass
class TradingConfig:
    timeframe: int
    max_positions: int
    max_daily_trades: int
    risk_per_trade: float
    min_volume: float
    max_volume: float
```

### RiskConfig
```python
@dataclass
class RiskConfig:
    max_drawdown: float
    stop_loss_atr_multiplier: float
    take_profit_atr_multiplier: float
    trailing_stop: bool
    max_spread: int
```

## Usage Examples

### Basic Bot Setup
```python
from src.core.bot import TradingBot
from src.utils.config import load_config

# Load configuration
config = load_config()

# Create and run bot
bot = TradingBot(config)
bot.run()
```

### Custom Strategy
```python
from src.strategy.gold_strategy import GoldTradingStrategy
from src.core.mt5_factory import create_mt5_client

# Create MT5 client
client = create_mt5_client(config.mt5.login, config.mt5.password, config.mt5.server)
client.connect()

# Create strategy
strategy = GoldTradingStrategy(config, client)

# Analyze market
market_state = strategy.analyze_market()
print(f"Market trend: {market_state.market_trend}")
print(f"AI confidence: {market_state.ai_confidence}")
```

### Backtesting
```python
from src.backtesting.backtest_engine import BacktestEngine
from src.utils.config import load_config

# Load configuration
config = load_config()

# Create backtest engine
engine = BacktestEngine(config)

# Run backtest
results = engine.run_backtest(data, start_date, end_date)

# Print results
print(f"Total Return: {results.total_return_pct:.2f}%")
print(f"Win Rate: {results.win_rate:.2f}%")
print(f"Max Drawdown: {results.max_drawdown_pct:.2f}%")
```

### Risk Management
```python
from src.risk.risk_manager import RiskManager
from src.utils.config import load_config

# Load configuration
config = load_config()

# Create risk manager
risk_manager = RiskManager(config.risk, config.trading)

# Calculate position size
position_size = risk_manager.calculate_position_size(
    balance=10000,
    entry_price=2000.0,
    stop_loss=1990.0,
    atr=15.0,
    symbol_info=symbol_info
)

print(f"Position size: {position_size.volume}")
print(f"Risk amount: ${position_size.risk_amount}")
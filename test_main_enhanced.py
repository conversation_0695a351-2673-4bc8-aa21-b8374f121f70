#!/usr/bin/env python3
"""
Test Main Enhanced Script
<PERSON> tra tất cả chức năng của main_enhanced.py
"""

import sys
import subprocess
import time
from pathlib import Path

def run_command(command, timeout=30):
    """Run command and return result"""
    try:
        print(f"🧪 Testing: {command}")
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        if result.returncode == 0:
            print(f"✅ SUCCESS")
            return True, result.stdout
        else:
            print(f"❌ FAILED: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT after {timeout} seconds")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False, str(e)


def test_help_command():
    """Test help command"""
    print("\n🧪 Testing Help Command")
    print("-" * 40)
    
    success, output = run_command("python main_enhanced.py --help", timeout=10)
    
    if success and "MT5 Enhanced Trading Bot" in output:
        print("✅ Help command works correctly")
        return True
    else:
        print("❌ Help command failed")
        return False


def test_config_loading():
    """Test configuration loading"""
    print("\n🧪 Testing Configuration Loading")
    print("-" * 42)
    
    # Test with default config
    test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from src.utils.config import load_config
    config = load_config()
    print("✅ Config loaded successfully")
    print(f"Symbol: {config.mt5.symbol}")
    print(f"Live trading: {config.live_trading}")
except Exception as e:
    print(f"❌ Config loading failed: {e}")
    sys.exit(1)
"""
    
    with open("temp_config_test.py", "w") as f:
        f.write(test_script)
    
    try:
        success, output = run_command("python temp_config_test.py", timeout=10)
        
        if success and "Config loaded successfully" in output:
            print("✅ Configuration loading works")
            return True
        else:
            print("❌ Configuration loading failed")
            return False
    finally:
        # Cleanup
        Path("temp_config_test.py").unlink(missing_ok=True)


def test_enhanced_components():
    """Test enhanced components initialization"""
    print("\n🧪 Testing Enhanced Components")
    print("-" * 42)
    
    test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from src.utils.config import load_config
    from src.risk.enhanced_risk_manager import EnhancedRiskManager
    from src.indicators.technical_indicators import TechnicalIndicators
    from src.strategy.adaptive_strategy import AdaptiveStrategy
    from src.performance.performance_tracker import PerformanceTracker
    
    config = load_config()
    
    # Test components
    risk_manager = EnhancedRiskManager(config)
    print("✅ Enhanced Risk Manager initialized")
    
    indicators = TechnicalIndicators(config)
    print("✅ Technical Indicators initialized")
    
    adaptive_strategy = AdaptiveStrategy(config)
    print("✅ Adaptive Strategy initialized")
    
    tracker = PerformanceTracker(10000.0)
    print("✅ Performance Tracker initialized")
    
    print("✅ All enhanced components working")
    
except Exception as e:
    print(f"❌ Enhanced components failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
    
    with open("temp_components_test.py", "w") as f:
        f.write(test_script)
    
    try:
        success, output = run_command("python temp_components_test.py", timeout=15)
        
        if success and "All enhanced components working" in output:
            print("✅ Enhanced components work correctly")
            return True
        else:
            print("❌ Enhanced components failed")
            print(f"Output: {output}")
            return False
    finally:
        # Cleanup
        Path("temp_components_test.py").unlink(missing_ok=True)


def test_backtest_mode():
    """Test backtest mode (quick test)"""
    print("\n🧪 Testing Backtest Mode")
    print("-" * 35)
    
    # This would normally require MT5 data, so we'll test the command parsing
    test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from src.backtesting.backtest_engine import SimpleBacktestConfig
    
    # Test quick setup
    config = SimpleBacktestConfig.quick_setup("1month", 10000.0)
    print(f"✅ Backtest config created: {config.start_date} to {config.end_date}")
    print(f"✅ Initial balance: ${config.initial_balance}")
    
    # Test custom setup
    custom_config = SimpleBacktestConfig(
        start_date="2024-01-01",
        end_date="2024-03-31",
        initial_balance=15000.0
    )
    print(f"✅ Custom config created: {custom_config.start_date} to {custom_config.end_date}")
    
    print("✅ Backtest configuration works")
    
except Exception as e:
    print(f"❌ Backtest configuration failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
    
    with open("temp_backtest_test.py", "w") as f:
        f.write(test_script)
    
    try:
        success, output = run_command("python temp_backtest_test.py", timeout=10)
        
        if success and "Backtest configuration works" in output:
            print("✅ Backtest mode configuration works")
            return True
        else:
            print("❌ Backtest mode failed")
            return False
    finally:
        # Cleanup
        Path("temp_backtest_test.py").unlink(missing_ok=True)


def test_optimization_framework():
    """Test optimization framework"""
    print("\n🧪 Testing Optimization Framework")
    print("-" * 44)
    
    test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from src.optimization.strategy_optimizer import StrategyOptimizer, OptimizationParameter
    
    # Test parameter creation
    param = OptimizationParameter("test_param", 10.0, 20.0, 2.0)
    print(f"✅ Optimization parameter created: {param.name}")
    
    # Test optimizer initialization
    optimizer = StrategyOptimizer()
    print("✅ Strategy optimizer initialized")
    
    # Test parameter generation
    combinations = optimizer._generate_parameter_combinations(max_combinations=10)
    print(f"✅ Generated {len(combinations)} parameter combinations")
    
    print("✅ Optimization framework works")
    
except Exception as e:
    print(f"❌ Optimization framework failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
    
    with open("temp_optimization_test.py", "w") as f:
        f.write(test_script)
    
    try:
        success, output = run_command("python temp_optimization_test.py", timeout=15)
        
        if success and "Optimization framework works" in output:
            print("✅ Optimization framework works")
            return True
        else:
            print("❌ Optimization framework failed")
            return False
    finally:
        # Cleanup
        Path("temp_optimization_test.py").unlink(missing_ok=True)


def test_performance_tracking():
    """Test performance tracking"""
    print("\n🧪 Testing Performance Tracking")
    print("-" * 42)
    
    test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from src.performance.performance_tracker import PerformanceTracker
    from src.performance.dashboard import PerformanceDashboard
    
    # Test tracker
    tracker = PerformanceTracker(10000.0)
    print(f"✅ Performance tracker created with balance: ${tracker.initial_balance}")
    
    # Test dashboard
    dashboard = PerformanceDashboard(tracker)
    print("✅ Performance dashboard created")
    
    # Test dashboard data generation
    dashboard_data = dashboard.generate_dashboard_data()
    print(f"✅ Dashboard data generated with {len(dashboard_data)} sections")
    
    print("✅ Performance tracking works")
    
except Exception as e:
    print(f"❌ Performance tracking failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
    
    with open("temp_performance_test.py", "w") as f:
        f.write(test_script)
    
    try:
        success, output = run_command("python temp_performance_test.py", timeout=10)
        
        if success and "Performance tracking works" in output:
            print("✅ Performance tracking works")
            return True
        else:
            print("❌ Performance tracking failed")
            return False
    finally:
        # Cleanup
        Path("temp_performance_test.py").unlink(missing_ok=True)


def main():
    """Run all tests for main_enhanced.py"""
    print("🚀 TESTING MAIN_ENHANCED.PY")
    print("=" * 60)
    print("Testing all components and functionality...")
    
    tests = [
        ("Help Command", test_help_command),
        ("Configuration Loading", test_config_loading),
        ("Enhanced Components", test_enhanced_components),
        ("Backtest Mode", test_backtest_mode),
        ("Optimization Framework", test_optimization_framework),
        ("Performance Tracking", test_performance_tracking)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ main_enhanced.py is ready to use!")
        print("\n🚀 NEXT STEPS:")
        print("1. python main_enhanced.py --mode demo    # Test with demo")
        print("2. python main_enhanced.py --mode backtest --period 1month")
        print("3. python main_enhanced.py --mode live    # Go live!")
        return True
    else:
        print("❌ Some tests failed. Please check the setup.")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Ensure all dependencies are installed")
        print("2. Check config/config.yaml exists")
        print("3. Verify all src/ files are present")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

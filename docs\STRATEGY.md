# Trading Strategy Guide - MT5 Trading Bot

## 🎯 Strategy Overview

The MT5 Trading Bot uses a hybrid approach combining traditional technical analysis with AI/ML techniques, specifically optimized for gold (XAU/USD) trading on 5-minute timeframes.

## 📊 Technical Analysis Components

### MACD (Moving Average Convergence Divergence)
- **Purpose**: Trend-following momentum indicator
- **Parameters**: 12, 26, 9 (standard settings)
- **Signals**:
  - **Bullish**: MACD line crosses above signal line
  - **Bearish**: MACD line crosses below signal line
  - **Divergence**: Price and MACD moving in opposite directions

### ATR (Average True Range)
- **Purpose**: Volatility measurement for position sizing
- **Period**: 14 (standard)
- **Usage**:
  - Dynamic stop-loss calculation
  - Position size adjustment
  - Volatility assessment

### Pivot Points
- **Purpose**: Support and resistance levels
- **Calculation**: Standard pivot point formula
- **Levels**: PP, R1, R2, R3, S1, S2, S3
- **Usage**: Entry/exit decision support

## 🤖 AI/ML Integration

### Deep Q-Network (DQN)
- **Type**: Reinforcement learning model
- **Input**: Technical indicators + market state
- **Output**: Action probabilities (Buy/Sell/Hold)
- **Features**:
  - Experience replay memory
  - Continuous learning
  - Market state recognition

### Feature Engineering
- **Technical Indicators**: MACD, ATR, Pivot Points
- **Market State**: Trend, volatility, spread
- **Temporal Features**: Time of day, day of week
- **Combined Signals**: Weighted indicator combinations

## 🎲 Signal Generation Logic

### Combined Decision Making
1. **Technical Analysis (70% weight)**
   - MACD crossover signals
   - Trend direction analysis
   - Pivot point levels
   - Volatility assessment

2. **AI Prediction (30% weight)**
   - DQN model prediction
   - Confidence scoring
   - Market state recognition

3. **Final Decision**
   - Weighted signal strength
   - Minimum threshold filtering
   - Risk validation

### Entry Conditions

#### Buy Signal
- MACD bullish crossover OR bullish trend
- Price above pivot point
- ATR within acceptable range
- AI confidence > 0.6
- No conflicting signals

#### Sell Signal
- MACD bearish crossover OR bearish trend
- Price below pivot point
- ATR within acceptable range
- AI confidence > 0.6
- No conflicting signals

#### Hold Signal
- Mixed or weak signals
- High spread conditions
- Low volatility periods
- AI confidence < 0.5

## ⚠️ Risk Management

### Position Sizing
```python
# ATR-based position sizing
risk_amount = account_balance * risk_per_trade
atr_distance = atr * stop_loss_multiplier
position_size = risk_amount / atr_distance
```

### Stop Loss & Take Profit
- **Stop Loss**: ATR × 2.0 (dynamic)
- **Take Profit**: ATR × 3.0 (1.5:1 risk-reward)
- **Trailing Stop**: Enabled for profit protection

### Risk Limits
- **Max Positions**: 3 concurrent trades
- **Max Daily Trades**: 10 trades per day
- **Max Drawdown**: 10% account balance
- **Max Spread**: 30 points

## 📈 Performance Metrics

### Key Indicators
- **Total Return**: Overall profit/loss percentage
- **Win Rate**: Percentage of winning trades
- **Profit Factor**: Gross profit / Gross loss
- **Sharpe Ratio**: Risk-adjusted returns
- **Max Drawdown**: Maximum loss from peak
- **Average Win/Loss**: Average profit/loss per trade

### Target Performance
- **Win Rate**: >55%
- **Profit Factor**: >1.5
- **Sharpe Ratio**: >1.0
- **Max Drawdown**: <10%

## 🔧 Strategy Customization

### Parameter Tuning
```yaml
trading:
  timeframe: 5             # 5-minute timeframe
  max_positions: 3         # Adjust based on risk tolerance
  risk_per_trade: 0.02     # 2% risk per trade

risk:
  stop_loss_atr_multiplier: 2.0   # Adjust SL sensitivity
  take_profit_atr_multiplier: 3.0 # Adjust TP targets
  max_drawdown: 0.10       # Adjust risk tolerance

ai:
  learning_rate: 0.001     # Model learning speed
  batch_size: 32           # Training batch size
  epsilon_decay: 0.995     # Exploration rate decay
```

### Market Conditions
The strategy adapts to different market conditions:

#### Trending Markets
- **Bullish**: Increased buy signal weight
- **Bearish**: Increased sell signal weight
- **Sideways**: Reduced position sizes

#### Volatile Markets
- **High ATR**: Smaller position sizes
- **Low ATR**: Normal position sizes
- **Extreme ATR**: Trading pause

#### Spread Conditions
- **Normal Spread**: Full trading
- **High Spread**: Reduced trading
- **Extreme Spread**: Trading pause

## 🧪 Strategy Testing

### Backtesting Process
1. **Historical Data**: Use real market data
2. **Walk-Forward Analysis**: Test on unseen data
3. **Parameter Optimization**: Find optimal settings
4. **Monte Carlo Simulation**: Test robustness

### Validation Metrics
- **In-Sample Performance**: Training period results
- **Out-of-Sample Performance**: Testing period results
- **Walk-Forward Performance**: Rolling window results
- **Monte Carlo Results**: Statistical significance

## 📊 Example Strategy Output

```
📊 MARKET ANALYSIS
🎯 Trend: Bullish
📈 MACD: Bullish crossover
📊 ATR: 15.2 (Medium volatility)
🎲 AI Confidence: 0.75
⚖️ Signal Strength: 0.82

📋 TRADING DECISION
✅ Action: BUY
💰 Entry Price: $2,045.50
🛑 Stop Loss: $2,030.50
🎯 Take Profit: $2,075.50
📊 Position Size: 0.1 lot
⚠️ Risk Amount: $150.00
```

## 🔄 Continuous Improvement

### Model Retraining
- **Frequency**: Weekly retraining
- **Data**: Recent market data
- **Validation**: Out-of-sample testing
- **Deployment**: Automatic model updates

### Performance Monitoring
- **Real-time Metrics**: Live performance tracking
- **Alert System**: Performance threshold alerts
- **Logging**: Detailed trade and decision logs
- **Analysis**: Regular performance reviews

### Strategy Evolution
- **Market Adaptation**: Adjust to changing conditions
- **Parameter Optimization**: Continuous parameter tuning
- **Feature Engineering**: Add new technical indicators
- **Risk Management**: Refine risk controls
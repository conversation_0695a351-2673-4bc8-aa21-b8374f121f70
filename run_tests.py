#!/usr/bin/env python3
"""
Test runner for MT5 Trading Bot
"""

import sys
import subprocess
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✅ SUCCESS")
        if result.stdout:
            print("Output:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED")
        print(f"Error: {e}")
        if e.stdout:
            print("Output:")
            print(e.stdout)
        if e.stderr:
            print("Error output:")
            print(e.stderr)
        return False

def main():
    """Main test runner"""
    print("MT5 Trading Bot - Test Suite")
    print("=" * 60)
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected")
        print("   Consider activating virtual environment first")
        print("   Run: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)")
    
    success_count = 0
    total_tests = 0
    
    # Test 1: Check Python version
    total_tests += 1
    if sys.version_info >= (3, 8):
        print("✅ Python version check passed")
        success_count += 1
    else:
        print("❌ Python version check failed - requires Python 3.8+")
    
    # Test 2: Install dependencies
    total_tests += 1
    if run_command("pip install -r requirements.txt", "Installing dependencies"):
        success_count += 1
    
    # Test 3: Code style check (if flake8 is available)
    total_tests += 1
    if run_command("python -m flake8 src/ --max-line-length=100 --ignore=E501,W503", 
                   "Code style check"):
        success_count += 1
    
    # Test 4: Type checking (if mypy is available)
    total_tests += 1
    if run_command("python -m mypy src/ --ignore-missing-imports", "Type checking"):
        success_count += 1
    
    # Test 5: Unit tests
    total_tests += 1
    if run_command("python -m pytest tests/ -v --tb=short", "Unit tests"):
        success_count += 1
    
    # Test 6: Import tests
    total_tests += 1
    if run_command("python -c \"from src.core.mt5_client import MT5Client; print('✅ MT5Client import OK')\"", 
                   "Import test - MT5Client"):
        success_count += 1
    
    # Test 7: Configuration validation
    total_tests += 1
    if run_command("python -c \"from src.utils.config import load_config; print('✅ Config loading OK')\"", 
                   "Configuration validation"):
        success_count += 1
    
    # Test 8: Indicators test
    total_tests += 1
    if run_command("python -c \"from src.indicators.technical_indicators import TechnicalIndicators; print('✅ Indicators import OK')\"", 
                   "Indicators import test"):
        success_count += 1
    
    # Test 9: AI model test
    total_tests += 1
    if run_command("python -c \"from src.models.dqn_agent import DQNAgent; print('✅ DQN Agent import OK')\"", 
                   "AI model import test"):
        success_count += 1
    
    # Test 10: Strategy test
    total_tests += 1
    if run_command("python -c \"from src.strategy.gold_strategy import GoldTradingStrategy; print('✅ Strategy import OK')\"", 
                   "Strategy import test"):
        success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Passed: {success_count}/{total_tests}")
    print(f"Success rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Bot is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

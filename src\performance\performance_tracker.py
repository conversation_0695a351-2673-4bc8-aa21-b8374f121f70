"""
Performance Tracking System
Real-time performance monitoring and analytics for the trading bot
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import json
from pathlib import Path

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TradeRecord:
    """Individual trade record"""
    trade_id: str
    symbol: str
    direction: str  # 'long' or 'short'
    entry_time: datetime
    exit_time: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    volume: float
    pnl: Optional[float]
    commission: float
    swap: float
    duration_minutes: Optional[float]
    market_condition: str
    signal_confidence: float
    stop_loss: float
    take_profit: float
    exit_reason: str  # 'tp', 'sl', 'signal_reversal', 'timeout', 'manual'


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    # Basic metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float

    # P&L metrics
    total_pnl: float
    gross_profit: float
    gross_loss: float
    profit_factor: float
    expectancy: float

    # Risk metrics
    max_drawdown: float
    max_drawdown_pct: float
    current_drawdown: float
    current_drawdown_pct: float

    # Risk-adjusted returns
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float

    # Trade statistics
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_trade_duration: float

    # Consecutive trades
    max_consecutive_wins: int
    max_consecutive_losses: int
    current_consecutive_wins: int
    current_consecutive_losses: int

    # Time-based metrics
    daily_return: float
    weekly_return: float
    monthly_return: float

    # Advanced metrics
    var_95: float  # Value at Risk
    kelly_criterion: float
    recovery_factor: float

    # Market condition performance
    trending_performance: Dict[str, float]
    ranging_performance: Dict[str, float]

    # Last updated
    last_updated: datetime


class PerformanceTracker:
    """Real-time performance tracking and analytics"""

    def __init__(self, initial_balance: float = 10000.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.peak_balance = initial_balance

        # Trade records
        self.trades: List[TradeRecord] = []
        self.open_trades: Dict[str, TradeRecord] = {}

        # Performance history
        self.daily_balances: List[Dict] = []
        self.equity_curve: List[Dict] = []

        # Performance cache
        self._metrics_cache: Optional[PerformanceMetrics] = None
        self._cache_timestamp: Optional[datetime] = None
        self._cache_duration = timedelta(minutes=1)  # Cache for 1 minute

        # Data persistence
        self.data_file = Path("data/performance_data.json")
        self.data_file.parent.mkdir(exist_ok=True)

        # Load existing data
        self._load_data()

    def record_trade_entry(self, trade_id: str, symbol: str, direction: str,
                          entry_price: float, volume: float, stop_loss: float,
                          take_profit: float, market_condition: str,
                          signal_confidence: float) -> None:
        """Record a new trade entry"""
        trade = TradeRecord(
            trade_id=trade_id,
            symbol=symbol,
            direction=direction,
            entry_time=datetime.now(),
            exit_time=None,
            entry_price=entry_price,
            exit_price=None,
            volume=volume,
            pnl=None,
            commission=0.0,
            swap=0.0,
            duration_minutes=None,
            market_condition=market_condition,
            signal_confidence=signal_confidence,
            stop_loss=stop_loss,
            take_profit=take_profit,
            exit_reason=""
        )

        self.open_trades[trade_id] = trade
        logger.info(f"Recorded trade entry: {trade_id} - {direction} {volume} {symbol} @ {entry_price}")

        # Invalidate cache
        self._invalidate_cache()

    def record_trade_exit(self, trade_id: str, exit_price: float,
                         commission: float, swap: float, exit_reason: str) -> None:
        """Record a trade exit"""
        if trade_id not in self.open_trades:
            logger.warning(f"Trade {trade_id} not found in open trades")
            return

        trade = self.open_trades[trade_id]
        trade.exit_time = datetime.now()
        trade.exit_price = exit_price
        trade.commission = commission
        trade.swap = swap
        trade.exit_reason = exit_reason

        # Calculate P&L
        if trade.direction == 'long':
            trade.pnl = (exit_price - trade.entry_price) * trade.volume - commission - swap
        else:  # short
            trade.pnl = (trade.entry_price - exit_price) * trade.volume - commission - swap

        # Calculate duration
        trade.duration_minutes = (trade.exit_time - trade.entry_time).total_seconds() / 60

        # Move to completed trades
        self.trades.append(trade)
        del self.open_trades[trade_id]

        # Update balance
        self.current_balance += trade.pnl
        self.peak_balance = max(self.peak_balance, self.current_balance)

        # Record equity point
        self.equity_curve.append({
            'timestamp': datetime.now(),
            'balance': self.current_balance,
            'trade_id': trade_id,
            'pnl': trade.pnl
        })

        logger.info(f"Recorded trade exit: {trade_id} - P&L: ${trade.pnl:.2f}, "
                   f"Balance: ${self.current_balance:.2f}")

        # Invalidate cache and save data
        self._invalidate_cache()
        self._save_data()

    def update_daily_balance(self) -> None:
        """Update daily balance record"""
        today = datetime.now().date()

        # Check if we already have today's record
        if self.daily_balances and self.daily_balances[-1]['date'] == today:
            # Update existing record
            self.daily_balances[-1]['balance'] = self.current_balance
            self.daily_balances[-1]['timestamp'] = datetime.now()
        else:
            # Add new daily record
            self.daily_balances.append({
                'date': today,
                'balance': self.current_balance,
                'timestamp': datetime.now()
            })

        # Keep only last 365 days
        if len(self.daily_balances) > 365:
            self.daily_balances = self.daily_balances[-365:]

    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics (cached)"""
        now = datetime.now()

        # Return cached metrics if still valid
        if (self._metrics_cache and self._cache_timestamp and
            now - self._cache_timestamp < self._cache_duration):
            return self._metrics_cache

        # Calculate fresh metrics
        metrics = self._calculate_metrics()

        # Cache the results
        self._metrics_cache = metrics
        self._cache_timestamp = now

        return metrics

    def _calculate_metrics(self) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        if not self.trades:
            return self._get_empty_metrics()

        # Basic trade statistics
        total_trades = len(self.trades)
        winning_trades = sum(1 for trade in self.trades if trade.pnl > 0)
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # P&L calculations
        pnls = [trade.pnl for trade in self.trades]
        total_pnl = sum(pnls)
        gross_profit = sum(pnl for pnl in pnls if pnl > 0)
        gross_loss = abs(sum(pnl for pnl in pnls if pnl < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        expectancy = total_pnl / total_trades if total_trades > 0 else 0

        # Win/Loss statistics
        winning_pnls = [pnl for pnl in pnls if pnl > 0]
        losing_pnls = [pnl for pnl in pnls if pnl < 0]

        avg_win = np.mean(winning_pnls) if winning_pnls else 0
        avg_loss = np.mean(losing_pnls) if losing_pnls else 0
        largest_win = max(winning_pnls) if winning_pnls else 0
        largest_loss = min(losing_pnls) if losing_pnls else 0

        # Drawdown calculations
        max_drawdown, max_drawdown_pct, current_drawdown, current_drawdown_pct = self._calculate_drawdowns()

        # Risk-adjusted metrics
        returns = self._get_returns_series()
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        sortino_ratio = self._calculate_sortino_ratio(returns)
        calmar_ratio = (self.current_balance / self.initial_balance - 1) / max_drawdown_pct if max_drawdown_pct > 0 else 0

        # Trade duration
        durations = [trade.duration_minutes for trade in self.trades if trade.duration_minutes]
        avg_trade_duration = np.mean(durations) if durations else 0

        # Consecutive trades
        max_cons_wins, max_cons_losses, curr_cons_wins, curr_cons_losses = self._calculate_consecutive_trades()

        # Time-based returns
        daily_return, weekly_return, monthly_return = self._calculate_time_returns()

        # Advanced metrics
        var_95 = self._calculate_var(returns, 0.95)
        kelly_criterion = self._calculate_kelly_criterion(winning_pnls, losing_pnls, win_rate)
        recovery_factor = total_pnl / abs(max_drawdown) if max_drawdown != 0 else 0

        # Market condition performance
        trending_perf, ranging_perf = self._calculate_market_condition_performance()

        return PerformanceMetrics(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            total_pnl=total_pnl,
            gross_profit=gross_profit,
            gross_loss=gross_loss,
            profit_factor=profit_factor,
            expectancy=expectancy,
            max_drawdown=max_drawdown,
            max_drawdown_pct=max_drawdown_pct,
            current_drawdown=current_drawdown,
            current_drawdown_pct=current_drawdown_pct,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            avg_win=avg_win,
            avg_loss=avg_loss,
            largest_win=largest_win,
            largest_loss=largest_loss,
            avg_trade_duration=avg_trade_duration,
            max_consecutive_wins=max_cons_wins,
            max_consecutive_losses=max_cons_losses,
            current_consecutive_wins=curr_cons_wins,
            current_consecutive_losses=curr_cons_losses,
            daily_return=daily_return,
            weekly_return=weekly_return,
            monthly_return=monthly_return,
            var_95=var_95,
            kelly_criterion=kelly_criterion,
            recovery_factor=recovery_factor,
            trending_performance=trending_perf,
            ranging_performance=ranging_perf,
            last_updated=datetime.now()
        )

    def _get_empty_metrics(self) -> PerformanceMetrics:
        """Return empty metrics when no trades exist"""
        return PerformanceMetrics(
            total_trades=0, winning_trades=0, losing_trades=0, win_rate=0,
            total_pnl=0, gross_profit=0, gross_loss=0, profit_factor=0, expectancy=0,
            max_drawdown=0, max_drawdown_pct=0, current_drawdown=0, current_drawdown_pct=0,
            sharpe_ratio=0, sortino_ratio=0, calmar_ratio=0,
            avg_win=0, avg_loss=0, largest_win=0, largest_loss=0, avg_trade_duration=0,
            max_consecutive_wins=0, max_consecutive_losses=0,
            current_consecutive_wins=0, current_consecutive_losses=0,
            daily_return=0, weekly_return=0, monthly_return=0,
            var_95=0, kelly_criterion=0, recovery_factor=0,
            trending_performance={}, ranging_performance={},
            last_updated=datetime.now()
        )

    def _calculate_drawdowns(self) -> Tuple[float, float, float, float]:
        """Calculate drawdown metrics"""
        if not self.equity_curve:
            return 0, 0, 0, 0

        balances = [point['balance'] for point in self.equity_curve]
        peak = self.initial_balance
        max_dd = 0
        max_dd_pct = 0

        for balance in balances:
            if balance > peak:
                peak = balance

            drawdown = peak - balance
            drawdown_pct = drawdown / peak if peak > 0 else 0

            if drawdown > max_dd:
                max_dd = drawdown
                max_dd_pct = drawdown_pct

        # Current drawdown
        current_dd = self.peak_balance - self.current_balance
        current_dd_pct = current_dd / self.peak_balance if self.peak_balance > 0 else 0

        return max_dd, max_dd_pct, current_dd, current_dd_pct

    def _get_returns_series(self) -> pd.Series:
        """Get returns series for risk calculations"""
        if len(self.equity_curve) < 2:
            return pd.Series([])

        balances = [point['balance'] for point in self.equity_curve]
        returns = pd.Series(balances).pct_change().dropna()
        return returns

    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) == 0 or returns.std() == 0:
            return 0

        # Annualized Sharpe ratio (assuming 252 trading days)
        return (returns.mean() / returns.std()) * np.sqrt(252)

    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """Calculate Sortino ratio"""
        if len(returns) == 0:
            return 0

        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0:
            return float('inf')

        downside_std = negative_returns.std()
        if downside_std == 0:
            return 0

        return (returns.mean() / downside_std) * np.sqrt(252)

    def _calculate_consecutive_trades(self) -> Tuple[int, int, int, int]:
        """Calculate consecutive wins/losses"""
        if not self.trades:
            return 0, 0, 0, 0

        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0

        consecutive_wins = 0
        consecutive_losses = 0

        for trade in self.trades:
            if trade.pnl > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_wins = max(max_wins, consecutive_wins)
            else:
                consecutive_losses += 1
                consecutive_wins = 0
                max_losses = max(max_losses, consecutive_losses)

        # Current consecutive
        current_wins = consecutive_wins
        current_losses = consecutive_losses

        return max_wins, max_losses, current_wins, current_losses

    def _calculate_time_returns(self) -> Tuple[float, float, float]:
        """Calculate time-based returns"""
        if not self.daily_balances:
            return 0, 0, 0

        now = datetime.now()

        # Daily return
        daily_return = 0
        if len(self.daily_balances) >= 2:
            yesterday_balance = self.daily_balances[-2]['balance']
            daily_return = (self.current_balance - yesterday_balance) / yesterday_balance

        # Weekly return
        weekly_return = 0
        week_ago = now - timedelta(days=7)
        for record in reversed(self.daily_balances):
            if record['timestamp'] <= week_ago:
                weekly_return = (self.current_balance - record['balance']) / record['balance']
                break

        # Monthly return
        monthly_return = 0
        month_ago = now - timedelta(days=30)
        for record in reversed(self.daily_balances):
            if record['timestamp'] <= month_ago:
                monthly_return = (self.current_balance - record['balance']) / record['balance']
                break

        return daily_return, weekly_return, monthly_return

    def _calculate_var(self, returns: pd.Series, confidence: float) -> float:
        """Calculate Value at Risk"""
        if len(returns) == 0:
            return 0
        return float(np.percentile(returns, (1 - confidence) * 100))

    def _calculate_kelly_criterion(self, winning_pnls: List[float],
                                 losing_pnls: List[float], win_rate: float) -> float:
        """Calculate Kelly Criterion for optimal position sizing"""
        if not winning_pnls or not losing_pnls:
            return 0

        avg_win = np.mean(winning_pnls)
        avg_loss = abs(np.mean(losing_pnls))

        if avg_loss == 0:
            return 0

        win_loss_ratio = avg_win / avg_loss
        kelly = win_rate - ((1 - win_rate) / win_loss_ratio)

        return max(0, min(1, kelly))  # Bound between 0 and 1

    def _calculate_market_condition_performance(self) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Calculate performance by market condition"""
        trending_trades = [t for t in self.trades if 'trending' in t.market_condition.lower()]
        ranging_trades = [t for t in self.trades if 'ranging' in t.market_condition.lower()]

        trending_perf = self._get_condition_stats(trending_trades)
        ranging_perf = self._get_condition_stats(ranging_trades)

        return trending_perf, ranging_perf

    def _get_condition_stats(self, trades: List[TradeRecord]) -> Dict[str, float]:
        """Get statistics for a specific market condition"""
        if not trades:
            return {'trades': 0, 'win_rate': 0, 'avg_pnl': 0, 'total_pnl': 0}

        wins = sum(1 for t in trades if t.pnl > 0)
        total_pnl = sum(t.pnl for t in trades)

        return {
            'trades': len(trades),
            'win_rate': wins / len(trades),
            'avg_pnl': total_pnl / len(trades),
            'total_pnl': total_pnl
        }

    def _invalidate_cache(self) -> None:
        """Invalidate the metrics cache"""
        self._metrics_cache = None
        self._cache_timestamp = None

    def _save_data(self) -> None:
        """Save performance data to file"""
        try:
            data = {
                'initial_balance': self.initial_balance,
                'current_balance': self.current_balance,
                'peak_balance': self.peak_balance,
                'trades': [asdict(trade) for trade in self.trades],
                'daily_balances': self.daily_balances,
                'equity_curve': self.equity_curve,
                'last_saved': datetime.now().isoformat()
            }

            # Convert datetime objects to strings
            for trade_data in data['trades']:
                if trade_data['entry_time'] and hasattr(trade_data['entry_time'], 'isoformat'):
                    trade_data['entry_time'] = trade_data['entry_time'].isoformat()
                if trade_data['exit_time'] and hasattr(trade_data['exit_time'], 'isoformat'):
                    trade_data['exit_time'] = trade_data['exit_time'].isoformat()

            for balance_data in data['daily_balances']:
                if hasattr(balance_data['date'], 'isoformat'):
                    balance_data['date'] = balance_data['date'].isoformat()
                if hasattr(balance_data['timestamp'], 'isoformat'):
                    balance_data['timestamp'] = balance_data['timestamp'].isoformat()

            for equity_data in data['equity_curve']:
                if hasattr(equity_data['timestamp'], 'isoformat'):
                    equity_data['timestamp'] = equity_data['timestamp'].isoformat()

            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            logger.error(f"Failed to save performance data: {e}")

    def _load_data(self) -> None:
        """Load performance data from file"""
        try:
            if not self.data_file.exists():
                return

            with open(self.data_file, 'r') as f:
                data = json.load(f)

            self.initial_balance = data.get('initial_balance', self.initial_balance)
            self.current_balance = data.get('current_balance', self.current_balance)
            self.peak_balance = data.get('peak_balance', self.peak_balance)

            # Load trades
            for trade_data in data.get('trades', []):
                trade = TradeRecord(**trade_data)
                # Convert string dates back to datetime
                if isinstance(trade.entry_time, str):
                    trade.entry_time = datetime.fromisoformat(trade.entry_time)
                if isinstance(trade.exit_time, str) and trade.exit_time:
                    trade.exit_time = datetime.fromisoformat(trade.exit_time)
                self.trades.append(trade)

            # Load daily balances
            for balance_data in data.get('daily_balances', []):
                balance_data['date'] = datetime.fromisoformat(balance_data['date']).date()
                balance_data['timestamp'] = datetime.fromisoformat(balance_data['timestamp'])
                self.daily_balances.append(balance_data)

            # Load equity curve
            for equity_data in data.get('equity_curve', []):
                equity_data['timestamp'] = datetime.fromisoformat(equity_data['timestamp'])
                self.equity_curve.append(equity_data)

            logger.info(f"Loaded performance data: {len(self.trades)} trades, "
                       f"Balance: ${self.current_balance:.2f}")

        except Exception as e:
            logger.error(f"Failed to load performance data: {e}")

    def get_performance_summary(self) -> Dict:
        """Get a comprehensive performance summary"""
        metrics = self.get_current_metrics()

        return {
            'account_info': {
                'initial_balance': self.initial_balance,
                'current_balance': self.current_balance,
                'peak_balance': self.peak_balance,
                'total_return': self.current_balance - self.initial_balance,
                'total_return_pct': (self.current_balance / self.initial_balance - 1) * 100
            },
            'trade_statistics': {
                'total_trades': metrics.total_trades,
                'winning_trades': metrics.winning_trades,
                'losing_trades': metrics.losing_trades,
                'win_rate': metrics.win_rate * 100,
                'profit_factor': metrics.profit_factor,
                'expectancy': metrics.expectancy
            },
            'risk_metrics': {
                'max_drawdown': metrics.max_drawdown,
                'max_drawdown_pct': metrics.max_drawdown_pct * 100,
                'current_drawdown': metrics.current_drawdown,
                'current_drawdown_pct': metrics.current_drawdown_pct * 100,
                'sharpe_ratio': metrics.sharpe_ratio,
                'sortino_ratio': metrics.sortino_ratio,
                'var_95': metrics.var_95
            },
            'performance_periods': {
                'daily_return': metrics.daily_return * 100,
                'weekly_return': metrics.weekly_return * 100,
                'monthly_return': metrics.monthly_return * 100
            },
            'market_conditions': {
                'trending': metrics.trending_performance,
                'ranging': metrics.ranging_performance
            },
            'open_trades': len(self.open_trades),
            'last_updated': metrics.last_updated.isoformat()
        }

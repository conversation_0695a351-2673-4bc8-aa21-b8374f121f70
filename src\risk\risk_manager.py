"""
Risk Management System for Gold Trading Bot
Implements position sizing, stop-loss, take-profit, and portfolio risk management
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import MetaTrader5 as mt5

from ..utils.logger import get_logger, TradingLogger
from ..utils.config import RiskConfig, TradingConfig
from ..core.mt5_client import PositionInfo

logger = get_logger(__name__)
trading_logger = TradingLogger("risk_management")


@dataclass
class RiskMetrics:
    """Enhanced risk metrics data structure"""
    current_drawdown: float
    max_drawdown: float
    total_risk_exposure: float
    daily_trades_count: int
    win_rate: float
    profit_factor: float
    sharpe_ratio: float
    var_95: float  # Value at Risk 95%
    consecutive_losses: int
    consecutive_wins: int
    avg_trade_duration: float
    volatility_adjusted_risk: float
    market_condition_risk: float


@dataclass
class PositionSize:
    """Enhanced position sizing calculation result"""
    volume: float
    risk_amount: float
    stop_loss: float
    take_profit: float
    risk_reward_ratio: float
    adjusted_volume: float  # Volume adjusted for market conditions
    confidence_multiplier: float  # Risk adjustment based on signal confidence
    volatility_adjustment: float  # Adjustment for current volatility
    max_position_time: int  # Maximum time to hold position (minutes)


class RiskManager:
    """Enhanced comprehensive risk management system with dynamic adjustments"""

    def __init__(self, risk_config: RiskConfig, trading_config: TradingConfig):
        self.risk_config = risk_config
        self.trading_config = trading_config
        self.current_balance = 0
        self.peak_balance = 0
        self.daily_trades = {}
        self.trade_history = []
        self.max_positions_violated = False  # Track violation state
        self.total_risk_violated = False  # Track TOTAL_RISK violation state

        # Enhanced risk tracking
        self.consecutive_losses = 0
        self.consecutive_wins = 0
        self.last_trade_result = None
        self.volatility_history = []
        self.position_timeouts = {}  # Track position entry times for dynamic timeouts
        self.market_condition_multiplier = 1.0  # Dynamic risk adjustment

        # Risk reduction triggers
        self.risk_reduction_active = False
        self.risk_reduction_factor = 1.0

    def calculate_position_size(self,
                              account_balance: float,
                              entry_price: float,
                              stop_loss: float,
                              atr_value: float,
                              symbol_info: Dict,
                              signal_confidence: float = 1.0,
                              market_volatility: str = 'medium',
                              market_regime: str = 'trending') -> PositionSize:
        """
        Calculate enhanced position size with dynamic risk adjustments

        Args:
            account_balance: Current account balance
            entry_price: Planned entry price
            stop_loss: Stop loss price
            atr_value: Current ATR value
            symbol_info: Symbol information from MT5
            signal_confidence: Signal confidence (0-1)
            market_volatility: Current market volatility ('low', 'medium', 'high', 'extreme')
            market_regime: Current market regime ('trending', 'ranging', 'breakout', 'reversal')

        Returns:
            Enhanced PositionSize object with dynamic adjustments
        """
        try:
            # Calculate base risk amount (percentage of balance)
            base_risk_amount = account_balance * self.trading_config.risk_per_trade

            # Apply dynamic risk adjustments
            risk_adjustments = self._calculate_risk_adjustments(
                signal_confidence, market_volatility, market_regime
            )

            # Adjust risk amount based on market conditions and performance
            adjusted_risk_amount = base_risk_amount * risk_adjustments['total_multiplier']

            # Calculate stop loss distance in price
            sl_distance = abs(entry_price - stop_loss)

            # If stop loss is not provided, use ATR-based stop loss with volatility adjustment
            if sl_distance == 0:
                atr_multiplier = self.risk_config.stop_loss_atr_multiplier
                # Adjust ATR multiplier based on volatility
                if market_volatility == 'low':
                    atr_multiplier *= 0.8  # Tighter stops in low volatility
                elif market_volatility == 'high':
                    atr_multiplier *= 1.3  # Wider stops in high volatility
                elif market_volatility == 'extreme':
                    atr_multiplier *= 1.5  # Much wider stops in extreme volatility

                sl_distance = atr_value * atr_multiplier
                if entry_price > stop_loss:  # Long position
                    stop_loss = entry_price - sl_distance
                else:  # Short position
                    stop_loss = entry_price + sl_distance

            # Calculate take profit with volatility adjustment
            tp_multiplier = self.risk_config.take_profit_atr_multiplier
            if market_volatility == 'low':
                tp_multiplier *= 0.9  # Closer targets in low volatility
            elif market_volatility == 'high':
                tp_multiplier *= 1.2  # Further targets in high volatility
            elif market_volatility == 'extreme':
                tp_multiplier *= 1.4  # Much further targets in extreme volatility

            tp_distance = atr_value * tp_multiplier
            if entry_price > stop_loss:  # Long position
                take_profit = entry_price + tp_distance
            else:  # Short position
                take_profit = entry_price - tp_distance

            # Calculate position size with enhanced logic
            contract_size = symbol_info.get('contract_size', 100)

            # Calculate base volume using adjusted risk amount
            base_volume = adjusted_risk_amount / (contract_size * sl_distance)

            trading_logger.log_warning("VOLUME_CALCULATION",
                f"Adjusted risk amount: {adjusted_risk_amount}, Base risk: {base_risk_amount}, Contract size: {contract_size}, SL distance: {sl_distance}, Raw volume: {base_volume}")

            # Apply volume constraints
            min_volume = symbol_info.get('volume_min', self.trading_config.min_volume)
            max_volume = symbol_info.get('volume_max', self.trading_config.max_volume)
            volume_step = symbol_info.get('volume_step', self.trading_config.volume_step)

            # Apply additional volume adjustments based on market conditions
            final_volume = base_volume * risk_adjustments['volume_multiplier']

            # Round to nearest volume step
            final_volume = round(final_volume / volume_step) * volume_step
            final_volume = max(min_volume, min(final_volume, max_volume))

            trading_logger.log_warning("VOLUME_FINAL",
                f"Final volume: {final_volume}, Adjustments: {risk_adjustments}")

            # Calculate risk-reward ratio
            tp_distance = abs(take_profit - entry_price)
            risk_reward_ratio = tp_distance / sl_distance if sl_distance > 0 else 0

            # Calculate dynamic position timeout based on market conditions
            max_position_time = self._calculate_position_timeout(market_volatility, market_regime)

            return PositionSize(
                volume=final_volume,
                risk_amount=adjusted_risk_amount,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                adjusted_volume=final_volume,
                confidence_multiplier=risk_adjustments['confidence_multiplier'],
                volatility_adjustment=risk_adjustments['volatility_multiplier'],
                max_position_time=max_position_time
            )

        except Exception as e:
            trading_logger.log_error("POSITION_SIZE_CALCULATION", f"Error calculating position size: {e}")
            logger.error(f"Error calculating position size: {e}")
            return PositionSize(
                volume=min_volume,
                risk_amount=0,
                stop_loss=stop_loss,
                take_profit=entry_price,
                risk_reward_ratio=0
            )

    def check_trading_allowed(self,
                            current_positions: List[PositionInfo],
                            account_info: Dict) -> Tuple[bool, str]:
        """
        Check if new trading is allowed based on risk rules

        Args:
            current_positions: List of current open positions
            account_info: Current account information

        Returns:
            Tuple of (allowed, reason)
        """
        # Update current balance
        self.current_balance = account_info.get('balance', 0)
        self.peak_balance = max(self.peak_balance, self.current_balance)

        # Check maximum positions limit
        if len(current_positions) >= self.trading_config.max_positions:
            reason = f"Maximum positions limit reached ({self.trading_config.max_positions})"
            trading_logger.log_risk_check("MAX_POSITIONS", False, reason, current_positions=len(current_positions), max_positions=self.trading_config.max_positions)
            return False, reason

        # Check daily trades limit
        today = datetime.now().date()
        daily_count = self.daily_trades.get(today, 0)
        if daily_count >= self.trading_config.max_daily_trades:
            reason = f"Daily trades limit reached ({self.trading_config.max_daily_trades})"
            trading_logger.log_risk_check("DAILY_TRADES", False, reason, daily_trades=daily_count, max_daily_trades=self.trading_config.max_daily_trades)
            return False, reason

        # Check maximum drawdown
        current_drawdown = self.calculate_current_drawdown(account_info)
        if current_drawdown > self.risk_config.max_drawdown:
            reason = f"Maximum drawdown exceeded ({current_drawdown:.2%} > {self.risk_config.max_drawdown:.2%})"
            trading_logger.log_risk_check("MAX_DRAWDOWN", False, reason, current_drawdown=f"{current_drawdown:.2%}", max_drawdown=f"{self.risk_config.max_drawdown:.2%}")
            return False, reason

        # Check margin level
        leverage = account_info.get('leverage', 0)
        if leverage < 200:  # Minimum 200% margin level
            reason = f"Insufficient margin level ({leverage:.1f}%)"
            trading_logger.log_risk_check("MARGIN_LEVEL", False, reason, margin_level=f"{leverage:.1f}%", min_required="200%")
            return False, reason

        # Check total risk exposure
        total_risk = self.calculate_total_risk_exposure(current_positions, account_info)
        max_total_risk = self.current_balance * self.risk_config.max_total_risk_exposure
        if total_risk > max_total_risk:
            reason = f"Total risk exposure too high ({total_risk:.2f} > {max_total_risk:.2f})"
            if not self.total_risk_violated:
                trading_logger.log_risk_check("TOTAL_RISK", False, reason, total_risk=f"{total_risk:.2f}", max_risk=f"{max_total_risk:.2f}")
                self.total_risk_violated = True
            else:
                trading_logger.log_risk_check("TOTAL_RISK", False, reason, total_risk=f"{total_risk:.2f}", max_risk=f"{max_total_risk:.2f}", repeat=True)
            return False, reason
        else:
            if self.total_risk_violated:
                # Reset state when back to valid
                self.total_risk_violated = False

        trading_logger.log_risk_check("TRADING_ALLOWED", True, "All risk checks passed")
        return True, "Trading allowed"

    def check_spread_condition(self, symbol_info: Dict) -> bool:
        """Check if spread is within acceptable limits"""
        spread = symbol_info.get('spread', 0)
        return spread <= self.risk_config.max_spread

    def calculate_current_drawdown(self, account_info: Dict) -> float:
        """Calculate current drawdown percentage"""
        current_equity = account_info.get('equity', 0)
        if self.peak_balance == 0:
            return 0.0

        drawdown = (self.peak_balance - current_equity) / self.peak_balance
        return max(0, drawdown)

    def calculate_total_risk_exposure(self,
                                    positions: List[PositionInfo],
                                    account_info: Dict) -> float:
        """Calculate total risk exposure from all positions"""
        total_risk = 0.0

        for position in positions:
            # Estimate risk based on position size and potential loss
            position_value = position.volume * position.price_current * 100  # Assuming contract size 100
            estimated_risk = position_value * 0.02  # Assume 2% risk per position
            total_risk += estimated_risk

        return total_risk

    def should_close_position(self,
                            position: PositionInfo,
                            current_price: float,
                            atr_value: float) -> Tuple[bool, str]:
        """
        Determine if a position should be closed based on risk rules

        Args:
            position: Position information
            current_price: Current market price
            atr_value: Current ATR value

        Returns:
            Tuple of (should_close, reason)
        """
        # Check trailing stop
        if self.risk_config.trailing_stop:
            if self._check_trailing_stop(position, current_price, atr_value):
                return True, "Trailing stop triggered"

        # Check maximum position age (e.g., 24 hours)
        position_age = datetime.now() - position.time
        if position_age > timedelta(hours=24):
            return True, "Maximum position age exceeded"

        # Check if position is significantly underwater
        unrealized_pnl_pct = position.profit / (position.volume * position.price_open * 100)
        if unrealized_pnl_pct < -0.05:  # More than 5% loss
            return True, "Position significantly underwater"

        return False, "Position within risk parameters"

    def _check_trailing_stop(self,
                           position: PositionInfo,
                           current_price: float,
                           atr_value: float) -> bool:
        """Check if trailing stop should be triggered"""
        trailing_distance = self.risk_config.trailing_stop_distance * 0.00001  # Convert points to price

        if position.type == mt5.ORDER_TYPE_BUY:
            # For long positions, trail below current price
            trailing_stop = current_price - trailing_distance
            return current_price <= trailing_stop
        else:
            # For short positions, trail above current price
            trailing_stop = current_price + trailing_distance
            return current_price >= trailing_stop

    def update_daily_trades(self):
        """Update daily trades counter"""
        today = datetime.now().date()
        self.daily_trades[today] = self.daily_trades.get(today, 0) + 1

        # Clean up old entries (keep only last 7 days)
        cutoff_date = today - timedelta(days=7)
        self.daily_trades = {
            date: count for date, count in self.daily_trades.items()
            if date >= cutoff_date
        }

    def add_trade_to_history(self, trade_data: Dict):
        """Add completed trade to history for analysis"""
        trade_data['timestamp'] = datetime.now()
        self.trade_history.append(trade_data)

        # Keep only last 1000 trades
        if len(self.trade_history) > 1000:
            self.trade_history = self.trade_history[-1000:]

    def calculate_risk_metrics(self, account_info: Dict) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        if not self.trade_history:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0)

        # Calculate current drawdown
        current_drawdown = self.calculate_current_drawdown(account_info)

        # Calculate maximum drawdown from trade history
        max_drawdown = self._calculate_max_drawdown()

        # Calculate win rate
        winning_trades = sum(1 for trade in self.trade_history if trade.get('profit', 0) > 0)
        win_rate = winning_trades / len(self.trade_history) if self.trade_history else 0

        # Calculate profit factor
        gross_profit = sum(trade.get('profit', 0) for trade in self.trade_history if trade.get('profit', 0) > 0)
        gross_loss = abs(sum(trade.get('profit', 0) for trade in self.trade_history if trade.get('profit', 0) < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Calculate Sharpe ratio (simplified)
        returns = [trade.get('profit', 0) for trade in self.trade_history]
        if returns:
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        # Calculate VaR 95%
        var_95 = np.percentile([trade.get('profit', 0) for trade in self.trade_history], 5) if self.trade_history else 0

        # Get today's trade count
        today = datetime.now().date()
        daily_trades_count = self.daily_trades.get(today, 0)

        return RiskMetrics(
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown,
            total_risk_exposure=0,  # Will be calculated separately
            daily_trades_count=daily_trades_count,
            win_rate=win_rate,
            profit_factor=profit_factor,
            sharpe_ratio=float(sharpe_ratio),
            var_95=float(var_95)
        )

    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from trade history"""
        if not self.trade_history:
            return 0.0

        # Calculate cumulative P&L
        cumulative_pnl = []
        running_total = 0
        for trade in self.trade_history:
            running_total += trade.get('profit', 0)
            cumulative_pnl.append(running_total)

        # Calculate maximum drawdown
        peak = cumulative_pnl[0]
        max_dd = 0

        for value in cumulative_pnl:
            if value > peak:
                peak = value
            drawdown = (peak - value) / abs(peak) if peak != 0 else 0
            max_dd = max(max_dd, drawdown)

        return max_dd

    def get_risk_summary(self, account_info: Dict, positions: List[PositionInfo]) -> Dict:
        """Get comprehensive risk summary"""
        metrics = self.calculate_risk_metrics(account_info)
        total_risk = self.calculate_total_risk_exposure(positions, account_info)

        return {
            'current_drawdown': f"{metrics.current_drawdown:.2%}",
            'max_drawdown': f"{metrics.max_drawdown:.2%}",
            'total_risk_exposure': f"${total_risk:.2f}",
            'daily_trades': metrics.daily_trades_count,
            'win_rate': f"{metrics.win_rate:.2%}",
            'profit_factor': f"{metrics.profit_factor:.2f}",
            'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
            'var_95': f"${metrics.var_95:.2f}",
            'account_balance': f"${account_info.get('balance', 0):.2f}",
            'account_equity': f"${account_info.get('equity', 0):.2f}",
            'margin_level': f"{account_info.get('margin_level', 0):.1f}%",
            'open_positions': len(positions)
        }

    def _calculate_risk_adjustments(self, signal_confidence: float,
                                  market_volatility: str, market_regime: str) -> Dict:
        """Calculate dynamic risk adjustments based on market conditions and performance"""

        # Base multipliers
        confidence_multiplier = 1.0
        volatility_multiplier = 1.0
        regime_multiplier = 1.0
        volume_multiplier = 1.0

        # Adjust based on signal confidence
        if signal_confidence < 0.5:
            confidence_multiplier = 0.5  # Reduce risk for low confidence signals
        elif signal_confidence < 0.7:
            confidence_multiplier = 0.75
        elif signal_confidence > 0.9:
            confidence_multiplier = 1.2  # Increase risk for high confidence signals

        # Adjust based on market volatility
        volatility_adjustments = {
            'low': 1.1,      # Slightly increase risk in low volatility
            'medium': 1.0,   # Normal risk
            'high': 0.8,     # Reduce risk in high volatility
            'extreme': 0.5   # Significantly reduce risk in extreme volatility
        }
        volatility_multiplier = volatility_adjustments.get(market_volatility, 1.0)

        # Adjust based on market regime
        regime_adjustments = {
            'trending': 1.1,    # Slightly increase risk in trending markets
            'ranging': 0.9,     # Reduce risk in ranging markets
            'breakout': 1.2,    # Increase risk for breakout trades
            'reversal': 0.8     # Reduce risk for reversal trades
        }
        regime_multiplier = regime_adjustments.get(market_regime, 1.0)

        # Adjust based on consecutive losses (risk reduction)
        if self.consecutive_losses >= 3:
            consecutive_loss_multiplier = max(0.3, 1.0 - (self.consecutive_losses - 2) * 0.1)
        else:
            consecutive_loss_multiplier = 1.0

        # Adjust based on consecutive wins (risk increase)
        if self.consecutive_wins >= 3:
            consecutive_win_multiplier = min(1.5, 1.0 + (self.consecutive_wins - 2) * 0.05)
        else:
            consecutive_win_multiplier = 1.0

        # Calculate total multiplier
        total_multiplier = (confidence_multiplier * volatility_multiplier *
                          regime_multiplier * consecutive_loss_multiplier *
                          consecutive_win_multiplier)

        # Ensure reasonable bounds
        total_multiplier = max(0.1, min(2.0, total_multiplier))
        volume_multiplier = max(0.5, min(1.5, total_multiplier))

        return {
            'confidence_multiplier': confidence_multiplier,
            'volatility_multiplier': volatility_multiplier,
            'regime_multiplier': regime_multiplier,
            'consecutive_loss_multiplier': consecutive_loss_multiplier,
            'consecutive_win_multiplier': consecutive_win_multiplier,
            'total_multiplier': total_multiplier,
            'volume_multiplier': volume_multiplier
        }

    def _calculate_position_timeout(self, market_volatility: str, market_regime: str) -> int:
        """Calculate dynamic position timeout in minutes based on market conditions"""

        # Base timeout (4 hours = 240 minutes)
        base_timeout = 240

        # Adjust based on volatility
        volatility_adjustments = {
            'low': 1.5,      # Hold longer in low volatility
            'medium': 1.0,   # Normal timeout
            'high': 0.7,     # Shorter timeout in high volatility
            'extreme': 0.5   # Much shorter timeout in extreme volatility
        }

        # Adjust based on market regime
        regime_adjustments = {
            'trending': 1.3,    # Hold longer in trending markets
            'ranging': 0.8,     # Shorter timeout in ranging markets
            'breakout': 1.5,    # Hold longer for breakouts
            'reversal': 0.6     # Shorter timeout for reversals
        }

        volatility_factor = volatility_adjustments.get(market_volatility, 1.0)
        regime_factor = regime_adjustments.get(market_regime, 1.0)

        # Calculate final timeout
        final_timeout = int(base_timeout * volatility_factor * regime_factor)

        # Ensure reasonable bounds (30 minutes to 12 hours)
        return max(30, min(720, final_timeout))

    def update_trade_result(self, profit: float):
        """Update consecutive wins/losses tracking"""
        if profit > 0:
            self.consecutive_wins += 1
            self.consecutive_losses = 0
            self.last_trade_result = 'win'
        else:
            self.consecutive_losses += 1
            self.consecutive_wins = 0
            self.last_trade_result = 'loss'

        # Add to trade history
        self.trade_history.append({
            'profit': profit,
            'timestamp': datetime.now(),
            'result': self.last_trade_result
        })

        # Keep only recent trades (last 100)
        if len(self.trade_history) > 100:
            self.trade_history = self.trade_history[-100:]

    def should_close_position_enhanced(self, position, current_price: float,
                                     atr_value: float, market_state) -> Tuple[bool, str]:
        """Enhanced position closing logic with dynamic timeouts and signal reversal"""

        # Check original closing conditions
        should_close, reason = self.should_close_position(position, current_price, atr_value)
        if should_close:
            return True, reason

        # Check dynamic timeout based on market conditions
        position_age_minutes = (datetime.now() - position.time).total_seconds() / 60
        max_timeout = self._calculate_position_timeout(
            market_state.market_regime.volatility_regime,
            market_state.market_regime.market_phase
        )

        if position_age_minutes > max_timeout:
            return True, f"Dynamic timeout exceeded ({position_age_minutes:.0f}min > {max_timeout}min)"

        # Check for signal reversal (as per user's memory)
        if hasattr(market_state, 'macd_signal'):
            position_type = 'long' if position.type == 1 else 'short'

            # Close long positions on bearish signals
            if (position_type == 'long' and
                market_state.macd_signal.crossover == 'bearish_cross'):
                return True, "MACD bearish crossover - closing long position"

            # Close short positions on bullish signals
            if (position_type == 'short' and
                market_state.macd_signal.crossover == 'bullish_cross'):
                return True, "MACD bullish crossover - closing short position"

        return False, "Position within acceptable parameters"

"""
Market Adaptation Engine
Dynamically adjusts strategy parameters based on market conditions
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..indicators.technical_indicators import MarketRegime
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class AdaptationParameters:
    """Strategy parameters that can be adapted"""
    signal_threshold: float = 0.6
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    atr_period: int = 14
    rsi_period: int = 14
    bb_period: int = 20
    bb_std: float = 2.0
    stop_loss_multiplier: float = 2.0
    take_profit_multiplier: float = 3.0
    max_position_time: int = 240  # minutes
    risk_per_trade: float = 0.02


@dataclass
class MarketCondition:
    """Current market condition assessment"""
    volatility_level: str  # 'low', 'medium', 'high', 'extreme'
    trend_strength: float  # 0-1
    trend_direction: str  # 'bullish', 'bearish', 'sideways'
    market_phase: str  # 'trending', 'ranging', 'breakout', 'reversal'
    efficiency: float  # Market efficiency score 0-1
    noise_level: float  # Market noise level 0-1


class MarketAdaptationEngine:
    """Engine for adapting strategy parameters to market conditions"""

    def __init__(self, base_config):
        self.base_config = base_config
        self.adaptation_history = []
        self.performance_tracking = {}
        self.current_parameters = AdaptationParameters()

        # Market condition thresholds
        self.volatility_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 1.0,
            'extreme': 1.5
        }

        # Performance tracking for different market conditions
        self.condition_performance = {
            'trending_bullish': {'trades': 0, 'wins': 0, 'total_pnl': 0},
            'trending_bearish': {'trades': 0, 'wins': 0, 'total_pnl': 0},
            'ranging': {'trades': 0, 'wins': 0, 'total_pnl': 0},
            'breakout': {'trades': 0, 'wins': 0, 'total_pnl': 0},
            'reversal': {'trades': 0, 'wins': 0, 'total_pnl': 0}
        }

    def assess_market_condition(self, market_data: pd.DataFrame,
                              market_regime: MarketRegime) -> MarketCondition:
        """Assess current market condition comprehensively"""

        # Calculate volatility level
        volatility_level = self._assess_volatility_level(market_data)

        # Calculate market efficiency (how well trends persist)
        efficiency = self._calculate_market_efficiency(market_data)

        # Calculate noise level (random price movements)
        noise_level = self._calculate_noise_level(market_data)

        return MarketCondition(
            volatility_level=volatility_level,
            trend_strength=market_regime.trend_strength,
            trend_direction=market_regime.trend_direction,
            market_phase=market_regime.market_phase,
            efficiency=efficiency,
            noise_level=noise_level
        )

    def adapt_parameters(self, market_condition: MarketCondition,
                        recent_performance: Dict) -> AdaptationParameters:
        """Adapt strategy parameters based on market conditions and performance"""

        # Start with base parameters
        adapted_params = AdaptationParameters()

        # Adapt based on market phase
        if market_condition.market_phase == 'trending':
            adapted_params = self._adapt_for_trending_market(adapted_params, market_condition)
        elif market_condition.market_phase == 'ranging':
            adapted_params = self._adapt_for_ranging_market(adapted_params, market_condition)
        elif market_condition.market_phase == 'breakout':
            adapted_params = self._adapt_for_breakout_market(adapted_params, market_condition)
        elif market_condition.market_phase == 'reversal':
            adapted_params = self._adapt_for_reversal_market(adapted_params, market_condition)

        # Adapt based on volatility
        adapted_params = self._adapt_for_volatility(adapted_params, market_condition)

        # Adapt based on recent performance
        adapted_params = self._adapt_for_performance(adapted_params, recent_performance)

        # Store adaptation history
        self.adaptation_history.append({
            'timestamp': datetime.now(),
            'market_condition': market_condition,
            'parameters': adapted_params,
            'performance': recent_performance
        })

        # Keep only recent history
        if len(self.adaptation_history) > 100:
            self.adaptation_history = self.adaptation_history[-100:]

        self.current_parameters = adapted_params
        return adapted_params

    def _adapt_for_trending_market(self, params: AdaptationParameters,
                                 condition: MarketCondition) -> AdaptationParameters:
        """Adapt parameters for trending markets"""

        # Use faster MACD for stronger trends
        if condition.trend_strength > 0.7:
            params.macd_fast = 8
            params.macd_slow = 21
            params.macd_signal = 5
        else:
            params.macd_fast = 12
            params.macd_slow = 26
            params.macd_signal = 9

        # Lower signal threshold for strong trends
        if condition.trend_strength > 0.8:
            params.signal_threshold = 0.5
        else:
            params.signal_threshold = 0.6

        # Wider stops and targets for trending markets
        params.stop_loss_multiplier = 2.5
        params.take_profit_multiplier = 4.0

        # Hold positions longer in trending markets
        params.max_position_time = int(360 * condition.trend_strength)

        return params

    def _adapt_for_ranging_market(self, params: AdaptationParameters,
                                condition: MarketCondition) -> AdaptationParameters:
        """Adapt parameters for ranging markets"""

        # Use slower MACD to reduce false signals
        params.macd_fast = 16
        params.macd_slow = 34
        params.macd_signal = 12

        # Higher signal threshold for ranging markets
        params.signal_threshold = 0.75

        # Tighter stops and targets for ranging markets
        params.stop_loss_multiplier = 1.5
        params.take_profit_multiplier = 2.0

        # Shorter position holding time
        params.max_position_time = 120

        # Reduce risk in ranging markets
        params.risk_per_trade = 0.015

        return params

    def _adapt_for_breakout_market(self, params: AdaptationParameters,
                                 condition: MarketCondition) -> AdaptationParameters:
        """Adapt parameters for breakout markets"""

        # Use medium-speed MACD for breakouts
        params.macd_fast = 10
        params.macd_slow = 24
        params.macd_signal = 7

        # Lower threshold for breakout signals
        params.signal_threshold = 0.55

        # Wider stops for breakouts (they can be volatile)
        params.stop_loss_multiplier = 3.0
        params.take_profit_multiplier = 5.0

        # Hold longer for breakouts to develop
        params.max_position_time = 480

        # Increase risk for high-probability breakouts
        params.risk_per_trade = 0.025

        return params

    def _adapt_for_reversal_market(self, params: AdaptationParameters,
                                 condition: MarketCondition) -> AdaptationParameters:
        """Adapt parameters for reversal markets"""

        # Use faster MACD to catch reversals quickly
        params.macd_fast = 6
        params.macd_slow = 18
        params.macd_signal = 4

        # Higher threshold for reversal signals (they're riskier)
        params.signal_threshold = 0.8

        # Tight stops for reversals
        params.stop_loss_multiplier = 1.8
        params.take_profit_multiplier = 2.5

        # Short holding time for reversals
        params.max_position_time = 90

        # Reduce risk for reversal trades
        params.risk_per_trade = 0.01

        return params

    def _adapt_for_volatility(self, params: AdaptationParameters,
                            condition: MarketCondition) -> AdaptationParameters:
        """Adapt parameters based on volatility level"""

        volatility_multipliers = {
            'low': {'stop': 0.8, 'target': 0.9, 'risk': 1.2},
            'medium': {'stop': 1.0, 'target': 1.0, 'risk': 1.0},
            'high': {'stop': 1.3, 'target': 1.2, 'risk': 0.8},
            'extreme': {'stop': 1.6, 'target': 1.4, 'risk': 0.5}
        }

        multiplier = volatility_multipliers.get(condition.volatility_level,
                                              volatility_multipliers['medium'])

        params.stop_loss_multiplier *= multiplier['stop']
        params.take_profit_multiplier *= multiplier['target']
        params.risk_per_trade *= multiplier['risk']

        return params

    def _adapt_for_performance(self, params: AdaptationParameters,
                             performance: Dict) -> AdaptationParameters:
        """Adapt parameters based on recent performance"""

        win_rate = performance.get('win_rate', 0.5)
        profit_factor = performance.get('profit_factor', 1.0)

        # If performance is poor, become more conservative
        if win_rate < 0.4 or profit_factor < 0.8:
            params.signal_threshold += 0.1
            params.risk_per_trade *= 0.8
            params.stop_loss_multiplier *= 0.9

        # If performance is excellent, become slightly more aggressive
        elif win_rate > 0.7 and profit_factor > 1.5:
            params.signal_threshold -= 0.05
            params.risk_per_trade *= 1.1
            params.take_profit_multiplier *= 1.1

        return params

    def _assess_volatility_level(self, market_data: pd.DataFrame) -> str:
        """Assess current volatility level"""
        if len(market_data) < 20:
            return 'medium'

        # Calculate rolling volatility
        returns = market_data['close'].pct_change().dropna()
        volatility = returns.rolling(window=20).std().iloc[-1] * np.sqrt(252)

        if volatility < self.volatility_thresholds['low']:
            return 'low'
        elif volatility < self.volatility_thresholds['medium']:
            return 'medium'
        elif volatility < self.volatility_thresholds['high']:
            return 'high'
        else:
            return 'extreme'

    def _calculate_market_efficiency(self, market_data: pd.DataFrame) -> float:
        """Calculate market efficiency (trend persistence)"""
        if len(market_data) < 50:
            return 0.5

        # Calculate directional movement efficiency
        price_changes = market_data['close'].diff().dropna()

        # Count consecutive moves in same direction
        consecutive_moves = 0
        total_moves = 0
        current_direction = None

        for change in price_changes:
            if change != 0:
                direction = 'up' if change > 0 else 'down'
                if direction == current_direction:
                    consecutive_moves += 1
                current_direction = direction
                total_moves += 1

        efficiency = consecutive_moves / total_moves if total_moves > 0 else 0.5
        return min(1.0, max(0.0, efficiency))

    def _calculate_noise_level(self, market_data: pd.DataFrame) -> float:
        """Calculate market noise level"""
        if len(market_data) < 20:
            return 0.5

        # Calculate noise as ratio of high-low range to close-open range
        high_low_range = (market_data['high'] - market_data['low']).rolling(window=20).mean()
        close_open_range = abs(market_data['close'] - market_data['open']).rolling(window=20).mean()

        noise_ratio = high_low_range.iloc[-1] / close_open_range.iloc[-1] if close_open_range.iloc[-1] > 0 else 1.0

        # Normalize to 0-1 scale
        normalized_noise = min(1.0, max(0.0, (noise_ratio - 1.0) / 3.0))
        return normalized_noise

    def update_performance(self, market_phase: str, trade_result: Dict):
        """Update performance tracking for different market conditions"""
        condition_key = market_phase

        if condition_key in self.condition_performance:
            perf = self.condition_performance[condition_key]
            perf['trades'] += 1
            perf['total_pnl'] += trade_result.get('pnl', 0)

            if trade_result.get('pnl', 0) > 0:
                perf['wins'] += 1

    def get_adaptation_summary(self) -> Dict:
        """Get summary of current adaptations"""
        if not self.adaptation_history:
            return {}

        latest = self.adaptation_history[-1]

        return {
            'timestamp': latest['timestamp'],
            'market_phase': latest['market_condition'].market_phase,
            'volatility_level': latest['market_condition'].volatility_level,
            'trend_strength': latest['market_condition'].trend_strength,
            'signal_threshold': latest['parameters'].signal_threshold,
            'macd_settings': {
                'fast': latest['parameters'].macd_fast,
                'slow': latest['parameters'].macd_slow,
                'signal': latest['parameters'].macd_signal
            },
            'risk_settings': {
                'risk_per_trade': latest['parameters'].risk_per_trade,
                'stop_loss_multiplier': latest['parameters'].stop_loss_multiplier,
                'take_profit_multiplier': latest['parameters'].take_profit_multiplier
            },
            'max_position_time': latest['parameters'].max_position_time
        }

    def get_performance_by_condition(self) -> Dict:
        """Get performance statistics by market condition"""
        results = {}

        for condition, perf in self.condition_performance.items():
            if perf['trades'] > 0:
                win_rate = perf['wins'] / perf['trades']
                avg_pnl = perf['total_pnl'] / perf['trades']

                results[condition] = {
                    'trades': perf['trades'],
                    'win_rate': win_rate,
                    'avg_pnl': avg_pnl,
                    'total_pnl': perf['total_pnl']
                }
            else:
                results[condition] = {
                    'trades': 0,
                    'win_rate': 0,
                    'avg_pnl': 0,
                    'total_pnl': 0
                }

        return results
